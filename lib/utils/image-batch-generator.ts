import { TITLE_TEMPLATES, CONTENT_TEMPLATES } from '@/lib/types/image-batch-generator'

export const generateTitle = (index: number, keywords: string[]) => {
  const template = TITLE_TEMPLATES[index % TITLE_TEMPLATES.length]
  const keyword = keywords[index % keywords.length] || "家居"
  const style = ["北欧", "现代", "简约", "奶油", "日式"][index % 5]
  const price = ["3万", "5万", "8万", "10万", "15万"][index % 5]
  
  return template
    .replace("{keyword}", keyword)
    .replace("{style}", style)
    .replace("{price}", price)
}

export const generateContent = (index: number, keywords: string[]) => {
  const template = CONTENT_TEMPLATES[index % CONTENT_TEMPLATES.length]
  const keyword = keywords[index % keywords.length] || "家居"
  const style = ["北欧风", "现代简约", "奶油风", "日式", "轻奢"][index % 5]
  const color = ["白色和原木色", "灰白色调", "米色系", "暖色调", "莫兰迪色"][index % 5]
  const area = [60, 80, 90, 110, 120][index % 5]
  const budget = [10, 15, 20, 25, 30][index % 5]
  const duration = [2, 3, 4, 5, 6][index % 5]
  
  return template
    .replace("{keyword}", keyword)
    .replace("{style}", style)
    .replace("{color}", color)
    .replace("{area}", area.toString())
    .replace("{budget}", budget.toString())
    .replace("{duration}", duration.toString())
    .replace("{highlight1}", "开放式厨房设计")
    .replace("{highlight2}", "整面收纳墙")
    .replace("{highlight3}", "无主灯设计")
    .replace("{material1}", "进口强化地板")
    .replace("{material2}", "乳胶漆+护墙板")
    .replace("{material3}", "宜家+网购")
    .replace("{furniture1}", "布艺沙发")
    .replace("{furniture2}", "实木茶几")
}