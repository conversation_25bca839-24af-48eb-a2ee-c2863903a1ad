interface TextOverlay {
  text: string
  position: "top-left" | "top-center" | "top-right" | "center" | "bottom-left" | "bottom-center" | "bottom-right"
  fontSize: number
  color: string
  bgColor: string
  fontWeight: "normal" | "bold"
  textStyle?: "normal" | "bold" | "outline" | "gradient" | "neon"
  shadowColor?: string
  subText?: string
  includeDecorations?: boolean
}

export async function processImageWithOverlay(
  imageFile: File,
  overlay: TextOverlay,
  keywords: string[]
): Promise<Blob> {
  // 确保在浏览器环境中运行
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    throw new Error('processImageWithOverlay must be called in a browser environment')
  }
  
  return new Promise((resolve, reject) => {
    const img = new Image()
    const reader = new FileReader()

    reader.onload = (e) => {
      img.src = e.target?.result as string
    }

    img.onload = () => {
      
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('Failed to get canvas context'))
        return
      }

      // 使用固定的画布尺寸，确保一致性
      const CANVAS_SIZE = 800
      canvas.width = CANVAS_SIZE
      canvas.height = CANVAS_SIZE

      // 计算图片缩放比例，保持比例并覆盖整个画布
      const scale = Math.max(CANVAS_SIZE / img.width, CANVAS_SIZE / img.height)
      const imgX = (CANVAS_SIZE / 2) - (img.width / 2) * scale
      const imgY = (CANVAS_SIZE / 2) - (img.height / 2) * scale
      
      // 绘制缩放后的图片
      ctx.drawImage(img, imgX, imgY, img.width * scale, img.height * scale)

      // 如果没有文字，直接返回原图
      if (!overlay.text || overlay.text.trim() === '') {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to convert canvas to blob'))
          }
        }, 'image/jpeg', 0.9)
        return
      }

      // 添加半透明遮罩层，让文字更突出
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
      ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE)

      // 设置文字样式
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      
      // 计算文字位置 - 使用简单的中心定位
      const centerX = CANVAS_SIZE / 2
      const centerY = CANVAS_SIZE / 2
      
      // 根据位置调整偏移量
      let x = centerX
      let y = centerY
      
      if (overlay.position.includes('top')) y = CANVAS_SIZE * 0.2
      else if (overlay.position.includes('bottom')) y = CANVAS_SIZE * 0.8
      
      if (overlay.position.includes('left')) {
        x = CANVAS_SIZE * 0.2
        ctx.textAlign = 'left'
      } else if (overlay.position.includes('right')) {
        x = CANVAS_SIZE * 0.8
        ctx.textAlign = 'right'
      }

      // 绘制主标题
      const mainY = overlay.position === 'center' ? y - 50 : y
      drawStyledText(ctx, overlay.text, x, mainY, overlay.fontSize, overlay.color, overlay.shadowColor || '#000000', overlay.textStyle || 'normal', overlay.fontWeight)
      
      // 绘制副标题
      if (overlay.subText) {
        const subTextLines = overlay.subText.split('\n')
        ctx.font = `${overlay.fontSize * 0.4}px Arial, sans-serif`
        ctx.fillStyle = overlay.color
        
        const subStartY = overlay.position === 'center' ? y + 50 : y + overlay.fontSize + 20
        subTextLines.forEach((line, index) => {
          ctx.fillText(line, x, subStartY + (index * overlay.fontSize * 0.5))
        })
      }
      
      // 清除阴影效果，避免影响后续绘制
      ctx.shadowColor = 'transparent'
      ctx.shadowBlur = 0
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0

      // 添加装饰元素
      if (overlay.includeDecorations !== false) {
        addDecorations(ctx, CANVAS_SIZE)
      }

      // 绘制关键词标签（更美观的样式）
      if (keywords.length > 0) {
        ctx.save() // 保存当前状态
        const tagFontSize = 20 // 固定标签字体大小
        ctx.font = `bold ${tagFontSize}px Arial, sans-serif`
        ctx.textAlign = 'left'
        ctx.textBaseline = 'bottom'
        
        keywords.slice(0, 5).forEach((keyword, index) => {
          const keywordX = 40 + index * 120
          const keywordY = CANVAS_SIZE - 40
          
          // 绘制标签背景
          const padding = tagFontSize / 3
          const metrics = ctx.measureText(`#${keyword}`)
          ctx.fillStyle = 'rgba(255, 255, 255, 0.9)'
          
          // 使用普通矩形代替roundRect（兼容性更好）
          ctx.fillRect(
            keywordX - padding,
            keywordY - tagFontSize - padding / 2,
            metrics.width + padding * 2,
            tagFontSize + padding
          )
          
          // 绘制标签文字
          ctx.fillStyle = '#333333'
          ctx.fillText(`#${keyword}`, keywordX, keywordY)
        })
        ctx.restore() // 恢复状态
      }

      // 转换为 Blob
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob)
        } else {
          reject(new Error('Failed to convert canvas to blob'))
        }
      }, 'image/jpeg', 0.95)
    }

    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }

    reader.readAsDataURL(imageFile)
  })
}

export async function createGalleryComposite(
  images: File[],
  template: string,
  includeWatermark: boolean
): Promise<Blob> {
  const compositeWidth = 1080
  const compositeHeight = 1350
  const margin = 10

  const canvas = document.createElement('canvas')
  canvas.width = compositeWidth
  canvas.height = compositeHeight
  const ctx = canvas.getContext('2d')
  
  if (!ctx) {
    throw new Error('Failed to get canvas context')
  }

  // 白色背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, compositeWidth, compositeHeight)

  // 计算布局
  const layouts = getGalleryLayout(images.length, compositeWidth, compositeHeight, margin)

  // 加载并绘制所有图片
  const loadPromises = images.slice(0, Math.min(images.length, layouts.length)).map((file, index) => {
    return new Promise<void>((resolve) => {
      const img = new Image()
      const reader = new FileReader()
      
      reader.onload = (e) => {
        img.src = e.target?.result as string
      }

      img.onload = () => {
        const layout = layouts[index]
        
        // 计算裁剪区域以保持比例
        const srcRatio = img.width / img.height
        const dstRatio = layout.width / layout.height
        
        let srcX = 0, srcY = 0, srcWidth = img.width, srcHeight = img.height
        
        if (srcRatio > dstRatio) {
          // 图片太宽，裁剪左右
          srcWidth = img.height * dstRatio
          srcX = (img.width - srcWidth) / 2
        } else {
          // 图片太高，裁剪上下
          srcHeight = img.width / dstRatio
          srcY = (img.height - srcHeight) / 2
        }

        ctx.drawImage(
          img,
          srcX, srcY, srcWidth, srcHeight,
          layout.x, layout.y, layout.width, layout.height
        )
        
        resolve()
      }

      reader.readAsDataURL(file)
    })
  })

  await Promise.all(loadPromises)

  // 添加水印
  if (includeWatermark) {
    ctx.font = '14px Arial'
    ctx.fillStyle = 'rgba(204, 204, 204, 0.5)'
    ctx.textAlign = 'right'
    ctx.fillText('推舟AI 制作', compositeWidth - 20, compositeHeight - 20)
  }

  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob)
      } else {
        reject(new Error('Failed to create gallery composite'))
      }
    }, 'image/jpeg', 0.9)
  })
}

function getGalleryLayout(
  imageCount: number,
  width: number,
  height: number,
  margin: number
): { x: number; y: number; width: number; height: number }[] {
  const layouts: { x: number; y: number; width: number; height: number }[] = []

  if (imageCount === 1) {
    layouts.push({ x: 0, y: 0, width, height })
  } else if (imageCount === 2) {
    const halfWidth = (width - margin) / 2
    layouts.push(
      { x: 0, y: 0, width: halfWidth, height },
      { x: halfWidth + margin, y: 0, width: halfWidth, height }
    )
  } else if (imageCount === 3) {
    const halfHeight = (height - margin) / 2
    const halfWidth = (width - margin) / 2
    layouts.push(
      { x: 0, y: 0, width, height: halfHeight },
      { x: 0, y: halfHeight + margin, width: halfWidth, height: halfHeight },
      { x: halfWidth + margin, y: halfHeight + margin, width: halfWidth, height: halfHeight }
    )
  } else if (imageCount === 4) {
    const halfWidth = (width - margin) / 2
    const halfHeight = (height - margin) / 2
    layouts.push(
      { x: 0, y: 0, width: halfWidth, height: halfHeight },
      { x: halfWidth + margin, y: 0, width: halfWidth, height: halfHeight },
      { x: 0, y: halfHeight + margin, width: halfWidth, height: halfHeight },
      { x: halfWidth + margin, y: halfHeight + margin, width: halfWidth, height: halfHeight }
    )
  } else {
    // 5张及以上，使用网格布局
    const gridSize = Math.ceil(Math.sqrt(imageCount))
    const cellWidth = (width - (gridSize - 1) * margin) / gridSize
    const cellHeight = (height - (gridSize - 1) * margin) / gridSize
    
    for (let i = 0; i < Math.min(imageCount, 9); i++) {
      const row = Math.floor(i / gridSize)
      const col = i % gridSize
      layouts.push({
        x: col * (cellWidth + margin),
        y: row * (cellHeight + margin),
        width: cellWidth,
        height: cellHeight
      })
    }
  }

  return layouts
}

// 绘制样式化文字
function drawStyledText(
  ctx: CanvasRenderingContext2D,
  text: string,
  x: number,
  y: number,
  size: number,
  color: string,
  shadowColor: string,
  style: string,
  fontWeight: string = 'normal'
) {
  // 设置字体
  ctx.font = fontWeight === 'bold' ? `bold ${size}px Arial, sans-serif` : `${size}px Arial, sans-serif`
  
  switch(style) {
    case 'outline':
      // 描边效果
      ctx.strokeStyle = shadowColor
      ctx.lineWidth = 8
      ctx.strokeText(text, x, y)
      ctx.fillStyle = color
      ctx.fillText(text, x, y)
      break
      
    case 'gradient':
      // 渐变效果
      const gradient = ctx.createLinearGradient(
        x - size * text.length / 4, 
        y - size / 2, 
        x + size * text.length / 4, 
        y + size / 2
      )
      gradient.addColorStop(0, color)
      gradient.addColorStop(0.5, '#ff6b6b')
      gradient.addColorStop(1, shadowColor)
      ctx.fillStyle = gradient
      ctx.fillText(text, x, y)
      break
      
    case 'neon':
      // 霓虹效果
      ctx.shadowColor = color
      ctx.shadowBlur = 20
      ctx.fillStyle = color
      ctx.fillText(text, x, y)
      ctx.shadowBlur = 40
      ctx.fillText(text, x, y)
      ctx.shadowBlur = 0
      break
      
    default:
      // 普通效果
      ctx.shadowColor = shadowColor
      ctx.shadowBlur = 10
      ctx.shadowOffsetX = 4
      ctx.shadowOffsetY = 4
      ctx.fillStyle = color
      ctx.fillText(text, x, y)
      ctx.shadowBlur = 0
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0
  }
}

// 添加装饰元素
function addDecorations(
  ctx: CanvasRenderingContext2D,
  canvasSize: number
) {
  // 添加角标
  ctx.save()
  ctx.translate(100, 100)
  ctx.rotate(-Math.PI / 4)
  ctx.fillStyle = '#ff6b6b'
  ctx.fillRect(-60, -20, 120, 40)
  ctx.fillStyle = 'white'
  ctx.font = 'bold 20px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText('HOT', 0, 0)
  ctx.restore()
  
  // 添加底部装饰线
  const bottomY = canvasSize - 50
  ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
  ctx.fillRect(50, bottomY, canvasSize - 100, 2)
  
  // 添加小图标
  ctx.font = '30px Arial'
  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
  ctx.fillText('✨', 50, 50)
  ctx.fillText('💫', canvasSize - 50, 50)
  ctx.fillText('🌟', 50, canvasSize - 50)
  ctx.fillText('⭐', canvasSize - 50, canvasSize - 50)
}