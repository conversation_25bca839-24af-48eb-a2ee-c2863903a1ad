export interface UploadedImage {
  id: string
  file: File
  preview: string
  order: number
}

export interface TextOverlay {
  text: string
  position: "top-left" | "top-center" | "top-right" | "center" | "bottom-left" | "bottom-center" | "bottom-right"
  fontSize: number
  color: string
  bgColor: string
  fontWeight: "normal" | "bold"
  textStyle?: "normal" | "bold" | "outline" | "gradient" | "neon"
  shadowColor?: string
  subText?: string
  includeDecorations?: boolean
}

export interface GeneratedNote {
  id: string
  title: string
  content: string
  coverImage: string
  images: string[]
  keywords: string[]
  textOverlay: TextOverlay
}

export interface GeneratorConfig {
  selectedTemplate: string
  autoGenerateTitle: boolean
  includeWatermark: boolean
  imageQuality: string
  autoVariation: boolean
  batchMode: boolean
  generationCount: number
}

// 标题模板
export const TITLE_TEMPLATES = [
  "把{keyword}搬进家啦～",
  "{keyword}｜{style}风格推荐",
  "新家装修好了，分享一下{keyword}",
  "{keyword}设计｜{style}奶油风",
  "北欧风{keyword}案例分享",
  "{keyword}布置分享｜{style}",
  "一镜到底看{keyword}",
  "{style}风{keyword}效果图",
  "花了{price}装出{keyword}",
  "{keyword}改造｜before&after"
]

// 内容模板
export const CONTENT_TEMPLATES = [
  `新家装修好了也有一段时间了，今天周末正好有空分享一下，{keyword}@家居薯

这个家是{style}的设计，整体配色以{color}为主。白色是北欧风设计中的重要元素，空间中的主要家具也是以白色和奶油色为主，所以整体感觉还是组合得很和谐的。家具多采用自然元素，搭配米色、灰色、绿色等浅色，北欧装修风格强调简洁，色彩和线条，整个家居风格很简洁舒适。`,
  
  `{keyword}终于完工啦！分享一下装修心得～

🏠 房屋信息：{area}平米 | {style}风格
💰 装修预算：{budget}万（硬装+软装）
⏰ 装修时长：{duration}个月

✨ 设计亮点：
1. {highlight1}
2. {highlight2}
3. {highlight3}

📍 材料清单：
- 地板：{material1}
- 墙面：{material2}
- 家具：{material3}`,

  `{keyword}分享 | {style}风格的温馨小家

最近刚搬进新家，迫不及待想要分享给大家！这次装修主打的就是{style}风格，整体色调以{color}为主。

客厅部分选择了{furniture1}，搭配{furniture2}，营造出温馨舒适的氛围。`
]

// 预设样式
export interface PresetStyle {
  name: string
  mainText: string
  subText: string
  textColor: string
  shadowColor: string
  textStyle: TextOverlay['textStyle']
  bgColor: string
  keywords: string[]
}

export const PRESET_STYLES: Record<string, PresetStyle> = {
  fashion: {
    name: '时尚风',
    mainText: '今日穿搭',
    subText: 'OOTD\n时尚日常',
    textColor: '#ffffff',
    shadowColor: '#000000',
    textStyle: 'outline',
    bgColor: 'rgba(0,0,0,0.3)',
    keywords: ['时尚', '穿搭', 'OOTD', '日常', '搭配']
  },
  food: {
    name: '美食风',
    mainText: '美食探店',
    subText: '必吃榜单\n人均50元',
    textColor: '#ffeb3b',
    shadowColor: '#333333',
    textStyle: 'bold',
    bgColor: 'rgba(0,0,0,0.4)',
    keywords: ['美食', '探店', '好吃', '推荐', '打卡']
  },
  travel: {
    name: '旅行风',
    mainText: '旅行攻略',
    subText: '3天2夜\n完美行程',
    textColor: '#00bcd4',
    shadowColor: '#004a5c',
    textStyle: 'gradient',
    bgColor: 'rgba(0,0,0,0.2)',
    keywords: ['旅行', '攻略', '打卡', '景点', '行程']
  },
  beauty: {
    name: '美妆风',
    mainText: '美妆教程',
    subText: '新手必看\n日常妆容',
    textColor: '#ff4081',
    shadowColor: '#8e0038',
    textStyle: 'neon',
    bgColor: 'rgba(255,255,255,0.1)',
    keywords: ['美妆', '教程', '化妆', '护肤', '分享']
  },
  study: {
    name: '学习风',
    mainText: '学习干货',
    subText: '高效方法\n提升效率',
    textColor: '#4caf50',
    shadowColor: '#1b5e20',
    textStyle: 'normal',
    bgColor: 'rgba(255,255,255,0.8)',
    keywords: ['学习', '干货', '方法', '效率', '提升']
  },
  life: {
    name: '生活风',
    mainText: '生活分享',
    subText: '日常vlog\n记录美好',
    textColor: '#9c27b0',
    shadowColor: '#4a148c',
    textStyle: 'bold',
    bgColor: 'rgba(0,0,0,0.3)',
    keywords: ['生活', '日常', 'vlog', '分享', '记录']
  },
  home: {
    name: '家居风',
    mainText: '家居好物',
    subText: '装修灵感\n温馨小家',
    textColor: '#ffffff',
    shadowColor: '#000000',
    textStyle: 'outline',
    bgColor: 'rgba(0,0,0,0.4)',
    keywords: ['家居', '装修', '设计', '好物', '温馨']
  }
}