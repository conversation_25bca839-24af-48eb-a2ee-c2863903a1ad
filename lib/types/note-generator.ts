export interface TextOverlay {
  text: string
  position: "top-left" | "top-center" | "top-right" | "center" | "bottom-left" | "bottom-center" | "bottom-right"
  fontSize: number
  color: string
  bgColor: string
  fontWeight: "normal" | "bold"
  fontStyle?: "italic" | "normal"
  textShadow?: boolean
}

export interface NoteTemplate {
  id: string
  name: string
  layout: "single" | "double" | "triple" | "quad" | "grid"
  style: "minimal" | "magazine" | "artistic" | "classic" | "modern"
}

export interface GeneratedNote {
  id: string
  title: string
  content: string
  coverImage: string
  images: string[]
  keywords: string[]
  template: NoteTemplate
  textOverlay: TextOverlay
  createdAt: Date
}

export interface NoteGenerationConfig {
  baseImages: File[]
  titleTemplate: string
  contentTemplate: string
  keywords: string[]
  textOverlay: TextOverlay
  template: NoteTemplate
  variations: {
    colorSchemes: string[][]
    fontSizes: number[]
    positions: TextOverlay["position"][]
    styles: string[]
  }
  count: number
}

export interface BatchGenerationResult {
  notes: GeneratedNote[]
  totalGenerated: number
  errors: string[]
}

export const DEFAULT_TEXT_OVERLAYS: TextOverlay[] = [
  {
    text: "清新简约风",
    position: "center",
    fontSize: 48,
    color: "#ffffff",
    bgColor: "#00000088",
    fontWeight: "bold"
  },
  {
    text: "生活美学",
    position: "bottom-left",
    fontSize: 36,
    color: "#333333",
    bgColor: "#ffffffcc",
    fontWeight: "normal"
  },
  {
    text: "每日好物",
    position: "top-center",
    fontSize: 42,
    color: "#ff6b6b",
    bgColor: "transparent",
    fontWeight: "bold",
    textShadow: true
  }
]

export const NOTE_TEMPLATES: NoteTemplate[] = [
  { id: "minimal", name: "极简风格", layout: "single", style: "minimal" },
  { id: "magazine", name: "杂志风格", layout: "double", style: "magazine" },
  { id: "artistic", name: "艺术风格", layout: "triple", style: "artistic" },
  { id: "classic", name: "经典风格", layout: "quad", style: "classic" },
  { id: "modern", name: "现代风格", layout: "grid", style: "modern" }
]

export const TITLE_TEMPLATES = [
  "把{keyword}搬进家啦～",
  "{keyword}｜{style}风格推荐",
  "新家装修好了，分享一下{keyword}",
  "{keyword}设计｜{style}奶油风",
  "北欧风{keyword}案例分享",
  "{keyword}布置分享｜{style}",
  "一镜到底看{keyword}",
  "{style}风{keyword}效果图",
  "花了{price}装出{keyword}",
  "{keyword}改造｜before&after"
]

export const CONTENT_TEMPLATES = [
  `新家装修好了也有一段时间了，今天周末正好有空分享一下，{keyword}@家居薯

这个家是{style}的设计，整体配色以{color}为主。白色是北欧风设计中的重要元素，空间中的主要家具也是以白色和奶油色为主，所以整体感觉还是组合得很和谐的。家具多采用自然元素，搭配米色、灰色、绿色等浅色，北欧装修风格强调简洁，色彩和线条，整个家居风格很简洁舒适。

@一贯一镜@好好设计`,
  
  `{keyword}终于完工啦！分享一下装修心得～

🏠 房屋信息：{area}平米 | {style}风格
💰 装修预算：{budget}万（硬装+软装）
⏰ 装修时长：{duration}个月

✨ 设计亮点：
1. {highlight1}
2. {highlight2} 
3. {highlight3}

📍 材料清单：
- 地板：{material1}
- 墙面：{material2}
- 家具：{material3}

整体效果还是很满意的，有问题可以评论区问我哦～`,

  `{keyword}分享 | {style}风格的温馨小家

最近刚搬进新家，迫不及待想要分享给大家！这次装修主打的就是{style}风格，整体色调以{color}为主。

客厅部分选择了{furniture1}，搭配{furniture2}，营造出温馨舒适的氛围。

卧室则采用了{bedroom_style}的设计理念，{bedroom_detail}。

厨房是我最满意的地方，{kitchen_detail}，每天做饭都有好心情！

希望能给正在装修的朋友一些灵感～`
]