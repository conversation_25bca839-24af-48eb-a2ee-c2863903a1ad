/**
 * 图片上传服务
 * 将图片上传到自定义图床
 */

const UPLOAD_API_URL = 'https://converimageupload.xhsinsight.com/'
const MAX_RETRIES = 3
const RETRY_DELAY = 2000 // 2秒
const REQUEST_TIMEOUT = 60000 // 30秒

/**
 * 将图片上传到自定义图床，支持重试机制
 * @param imageUrl 原始图片URL
 * @returns 新的图片URL，如果上传失败则返回原URL
 */
export async function uploadImageToCustomHost(imageUrl: string): Promise<string> {
  if (!imageUrl) {
    console.warn('[图床上传] 图片URL为空，无法上传')
    return imageUrl
  }

  // 重试机制
  for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
    try {
      if (attempt > 0) {
        console.log(`[图床上传] 第 ${attempt} 次重试...`)
        await delay(RETRY_DELAY)
      } else {
        console.log(`[图床上传] 正在上传图片到自定义图床: ${imageUrl}`)
      }

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT)

      const response = await fetch(UPLOAD_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ imageUrl }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (response.status === 200) {
        const result = await response.json()
        const newUrl = result.url
        
        if (newUrl) {
          console.log(`[图床上传] 图片上传成功，新URL: ${newUrl}`)
          return newUrl
        } else {
          if (attempt < MAX_RETRIES) {
            console.warn(`[图床上传] API返回数据中没有找到URL (尝试 ${attempt + 1}/${MAX_RETRIES + 1}): ${JSON.stringify(result)}，将重试...`)
            continue
          } else {
            console.error(`[图床上传] 在所有重试后仍然没有返回URL: ${JSON.stringify(result)}`)
            break
          }
        }
      } else {
        if (attempt < MAX_RETRIES) {
          console.warn(`[图床上传] API请求失败 (尝试 ${attempt + 1}/${MAX_RETRIES + 1})，状态码: ${response.status}，将重试...`)
          continue
        } else {
          console.error(`[图床上传] 在所有重试后仍然请求失败，状态码: ${response.status}`)
          break
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error(`[图床上传] 请求超时 (${REQUEST_TIMEOUT}ms)`)
      } else {
        console.error(`[图床上传] 发生异常 (尝试 ${attempt + 1}/${MAX_RETRIES + 1}):`, error)
      }
      
      if (attempt < MAX_RETRIES) {
        console.warn(`[图床上传] 将重试...`)
        continue
      } else {
        console.error(`[图床上传] 在所有重试后仍然发生异常`)
        break
      }
    }
  }

  // 如果所有尝试都失败，返回原URL
  console.warn(`[图床上传] 最终失败，保持原URL: ${imageUrl}`)
  return imageUrl
}

/**
 * 延迟函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}