import { pointsService } from './points-service'

/**
 * 通用积分集成服务
 * 提供简单易用的积分扣减接口，供其他功能模块调用
 */
export class PointsIntegrationService {
  
  /**
   * 检查用户是否有足够积分进行操作
   */
  static async checkBalance(
    userId: string, 
    operation: 'ai_rewrite' | 'viral_note' | 'image_generation' | 'ai_expert_chat' | 'note_extract' | 'sentiment_analysis' | 'ai_report' | 'custom',
    params?: { 
      imageCount?: number 
      customCost?: number
      modelName?: string
    }
  ): Promise<{
    sufficient: boolean
    currentBalance: number
    requiredPoints: number
    shortfall: number
  }> {
    let requiredPoints = 0

    switch (operation) {
      case 'ai_rewrite':
        // AI改写通常消耗15积分
        requiredPoints = 15
        break
      
      case 'viral_note':
        // 爆款笔记生成根据图片数量计算
        const imageCount = params?.imageCount || 3
        requiredPoints = 5 + (5 * imageCount) // 文本5 + 图片5*数量
        break
      
      case 'image_generation':
        // 单张图片生成
        requiredPoints = 15
        break
      
      case 'ai_expert_chat':
        // AI专家聊天
        requiredPoints = 1
        break
      
      case 'note_extract':
        // 笔记提取
        requiredPoints = 1
        break
      
      case 'sentiment_analysis':
        // 舆情分析
        requiredPoints = 30
        break
      
      case 'ai_report':
        // AI报告生成
        requiredPoints = 20
        break
      
      case 'custom':
        // 自定义积分消耗
        requiredPoints = params?.customCost || 0
        break
      
      default:
        throw new Error(`未知的操作类型: ${operation}`)
    }

    if (operation === 'ai_rewrite' && params?.modelName) {
      // 如果指定了模型，使用模型配置的积分消耗
      const modelConfig = await pointsService.getModelConfig(params.modelName)
      if (modelConfig) {
        requiredPoints = modelConfig.pointsCost
      }
    }

    const currentBalance = await pointsService.getBalance(userId)
    const sufficient = currentBalance >= requiredPoints
    const shortfall = sufficient ? 0 : requiredPoints - currentBalance

    return {
      sufficient,
      currentBalance,
      requiredPoints,
      shortfall
    }
  }

  /**
   * 执行积分扣减
   */
  static async consumePoints(
    userId: string,
    operation: 'ai_rewrite' | 'viral_note' | 'image_generation' | 'ai_expert_chat' | 'note_extract' | 'sentiment_analysis' | 'ai_report' | 'custom',
    params: {
      // 通用参数
      relatedId: string  // 关联的任务ID、笔记ID等
      description?: string
      metadata?: any
      
      // 爆款笔记参数
      imageCount?: number
      topic?: string
      writingStyle?: string
      imageStyle?: string
      
      // AI改写参数  
      modelName?: string
      noteId?: string
      
      // AI专家参数
      sessionId?: string
      message?: string
      
      // 笔记提取参数
      url?: string
      extractedNoteId?: string
      
      // 自定义参数
      customCost?: number
    }
  ) {
    switch (operation) {
      case 'ai_rewrite':
        if (!params.modelName) {
          throw new Error('AI改写操作需要指定modelName')
        }
        
        return await pointsService.consumePoints({
          userId,
          modelName: params.modelName,
          usageType: 'ai_rewrite',
          metadata: {
            noteId: params.noteId,
            relatedId: params.relatedId,
            ...params.metadata
          }
        })

      case 'viral_note':
        if (!params.imageCount) {
          throw new Error('病毒式笔记生成需要指定imageCount')
        }
        
        return await pointsService.consumePointsForViralGeneration({
          userId,
          imageCount: params.imageCount,
          taskId: params.relatedId,
          topic: params.topic || '',
          writingStyle: params.writingStyle || '',
          imageStyle: params.imageStyle || ''
        })

      case 'image_generation':
        return await pointsService.consumePoints({
          userId,
          modelName: 'image/generation',
          usageType: 'image_generation', 
          metadata: {
            relatedId: params.relatedId,
            description: params.description,
            ...params.metadata
          }
        })

      case 'ai_expert_chat':
        return await pointsService.consumePoints({
          userId,
          modelName: 'ai_expert_chat',
          usageType: 'ai_expert_chat',
          metadata: {
            sessionId: params.sessionId,
            message: params.message?.substring(0, 100),
            relatedId: params.relatedId,
            ...params.metadata
          }
        })

      case 'note_extract':
        return await pointsService.consumePoints({
          userId,
          modelName: 'note_extract',
          usageType: 'note_extraction',
          metadata: {
            url: params.url,
            extractedNoteId: params.extractedNoteId,
            relatedId: params.relatedId,
            ...params.metadata
          }
        })

      case 'sentiment_analysis':
        return await pointsService.consumePoints({
          userId,
          modelName: 'sentiment_analysis',
          usageType: 'sentiment_analysis',
          metadata: {
            keyword: params.metadata?.keyword,
            relatedId: params.relatedId,
            description: params.description || `舆情分析：${params.metadata?.keyword || ''}`,
            ...params.metadata
          }
        })

      case 'ai_report':
        return await pointsService.consumePoints({
          userId,
          modelName: 'ai_report_generation',
          usageType: 'ai_report',
          metadata: {
            cacheId: params.metadata?.cacheId,
            relatedId: params.relatedId,
            description: params.description || 'AI分析报告生成',
            ...params.metadata
          }
        })

      case 'custom':
        if (!params.customCost) {
          throw new Error('自定义操作需要指定customCost')
        }
        
        // 对于自定义操作，直接使用transaction来扣减积分
        const wallet = await pointsService.getOrCreateWallet(userId)
        if (wallet.balance < params.customCost) {
          return { 
            success: false, 
            remainingBalance: wallet.balance, 
            error: `积分余额不足，需要 ${params.customCost} 积分，当前余额 ${wallet.balance} 积分` 
          }
        }
        
        // 这里需要直接使用数据库事务，暂时抛出错误提示完善
        throw new Error('自定义积分扣减功能需要进一步实现')

      default:
        throw new Error(`未知的操作类型: ${operation}`)
    }
  }

  /**
   * 退还积分（用于操作失败时）
   */
  static async refundPoints(
    userId: string,
    amount: number,
    reason: string,
    relatedTransactionId?: string
  ) {
    return await pointsService.refundPoints({
      userId,
      amount,
      reason,
      relatedTransactionId
    })
  }

  /**
   * 获取用户积分余额
   */
  static async getBalance(userId: string): Promise<number> {
    return await pointsService.getBalance(userId)
  }

  /**
   * 获取用户积分统计
   */
  static async getStats(userId: string) {
    return await pointsService.getUserPointsStats(userId)
  }

  /**
   * 获取操作的积分消耗说明
   */
  static getOperationCostDescription(
    operation: 'ai_rewrite' | 'viral_note' | 'image_generation' | 'ai_expert_chat' | 'note_extract' | 'sentiment_analysis' | 'ai_report' | 'custom',
    params?: { imageCount?: number; customCost?: number }
  ): string {
    switch (operation) {
      case 'ai_rewrite':
        return 'AI改写笔记消耗 15 积分'
      
      case 'viral_note':
        const imageCount = params?.imageCount || 3
        const totalCost = 5 + (5 * imageCount)
        return `病毒式笔记生成消耗 ${totalCost} 积分（文本生成 5 + 图片生成 ${5 * imageCount}）`
      
      case 'image_generation':
        return '图片生成消耗 15 积分'
      
      case 'ai_expert_chat':
        return 'AI专家问答消耗 1 积分'
      
      case 'note_extract':
        return '笔记提取消耗 1 积分'
      
      case 'sentiment_analysis':
        return '舆情分析消耗 30 积分'
      
      case 'ai_report':
        return 'AI分析报告生成消耗 20 积分'
      
      case 'custom':
        return `自定义操作消耗 ${params?.customCost || 0} 积分`
      
      default:
        return '未知操作'
    }
  }
}

// 导出便捷的静态方法
export const pointsIntegration = {
  checkBalance: PointsIntegrationService.checkBalance,
  consumePoints: PointsIntegrationService.consumePoints,
  refundPoints: PointsIntegrationService.refundPoints,
  getBalance: PointsIntegrationService.getBalance,
  getStats: PointsIntegrationService.getStats,
  getCostDescription: PointsIntegrationService.getOperationCostDescription
}