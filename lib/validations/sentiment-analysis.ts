import { z } from "zod"

export const searchNotesSchema = z.object({
  keyword: z.string().min(1, "关键词不能为空").max(100, "关键词长度不能超过100个字符"),
  limit: z.number().int().min(1).max(200).optional().default(100),
  offset: z.number().int().min(0).optional().default(0)
})

export const analyzeRequestSchema = z.object({
  keyword: z.string().min(1, "关键词不能为空").max(100, "关键词长度不能超过100个字符"),
  forceRefresh: z.boolean().optional().default(false)
})

export const generateReportSchema = z.object({
  cacheId: z.string().uuid("无效的缓存ID"),
  forceRefresh: z.boolean().optional().default(false)
})

export type SearchNotesRequest = z.infer<typeof searchNotesSchema>
export type AnalyzeRequest = z.infer<typeof analyzeRequestSchema>
export type GenerateReportRequest = z.infer<typeof generateReportSchema>