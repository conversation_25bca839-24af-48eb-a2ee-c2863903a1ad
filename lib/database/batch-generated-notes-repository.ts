import { prisma } from './mysql-client'
import type { 
  BatchGeneratedNote, 
  BatchGenerationSession,
  Prisma 
} from '@prisma/client'

export interface CreateBatchGeneratedNoteData {
  userId: string
  title: string
  content: string
  coverImage: string
  contentImages: any[]
  batchSessionId: string
  batchIndex: number
  keywords: string[]
  templateSettings?: any
  textOverlayConfig?: any
  generationConfig?: any
}

export interface UpdateBatchGeneratedNoteData {
  title?: string
  content?: string
  coverImage?: string
  contentImages?: any[]
  isAddedToPublish?: boolean
  publishedNoteId?: string
}

export interface BatchGeneratedNoteFilters {
  userId?: string
  batchSessionId?: string
  isAddedToPublish?: boolean
  page?: number
  limit?: number
  sortBy?: 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}

export interface CreateBatchGenerationSessionData {
  userId: string
  sessionName: string
  totalGenerated?: number
  metadata?: any
}

export class BatchGeneratedNotesRepository {
  
  // Create batch generation session
  async createBatchSession(data: CreateBatchGenerationSessionData): Promise<BatchGenerationSession> {
    return await prisma.batchGenerationSession.create({
      data
    })
  }

  // Get batch sessions for user
  async getBatchSessions(userId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit

    const [sessions, total] = await Promise.all([
      prisma.batchGenerationSession.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.batchGenerationSession.count({
        where: { userId }
      })
    ])

    return {
      data: sessions,
      count: total,
      page,
      limit
    }
  }

  // Update batch session
  async updateBatchSession(id: string, totalGenerated: number) {
    return await prisma.batchGenerationSession.update({
      where: { id },
      data: { totalGenerated }
    })
  }

  // Create single batch generated note
  async createBatchGeneratedNote(data: CreateBatchGeneratedNoteData): Promise<BatchGeneratedNote> {
    return await prisma.batchGeneratedNote.create({
      data
    })
  }

  // Create multiple batch generated notes
  async createBatchGeneratedNotes(notes: CreateBatchGeneratedNoteData[]): Promise<{ count: number }> {
    try {
      console.log('Creating batch notes, count:', notes.length)
      
      // 验证数据
      if (notes.length === 0) {
        throw new Error('No notes to create')
      }
      
      // 检查第一个笔记的数据结构
      const firstNote = notes[0]
      console.log('First note structure:', {
        hasUserId: !!firstNote.userId,
        hasBatchSessionId: !!firstNote.batchSessionId,
        hasTitle: !!firstNote.title,
        hasContent: !!firstNote.content,
        hasCoverImage: !!firstNote.coverImage,
        contentImagesLength: Array.isArray(firstNote.contentImages) ? firstNote.contentImages.length : 'not array',
        keywordsLength: Array.isArray(firstNote.keywords) ? firstNote.keywords.length : 'not array'
      })
      
      const result = await prisma.batchGeneratedNote.createMany({
        data: notes,
        skipDuplicates: true
      })
      
      console.log('Batch create result:', result)
      
      // 验证创建结果
      const createdNotes = await prisma.batchGeneratedNote.count({
        where: {
          batchSessionId: notes[0].batchSessionId
        }
      })
      console.log('Verified created notes count:', createdNotes)
      
      return { count: result.count }
    } catch (error) {
      console.error('Error in createBatchGeneratedNotes:', error)
      throw error
    }
  }

  // Get batch generated notes
  async getBatchGeneratedNotes(filters: BatchGeneratedNoteFilters) {
    const page = filters.page || 1
    const limit = filters.limit || 10
    const skip = (page - 1) * limit
    const sortBy = filters.sortBy || 'createdAt'
    const sortOrder = filters.sortOrder || 'desc'

    const where: Prisma.BatchGeneratedNoteWhereInput = {}
    
    if (filters.userId) {
      where.userId = filters.userId
    }
    
    if (filters.batchSessionId) {
      where.batchSessionId = filters.batchSessionId
    }
    
    if (filters.isAddedToPublish !== undefined) {
      where.isAddedToPublish = filters.isAddedToPublish
    }

    // 先获取总数
    const total = await prisma.batchGeneratedNote.count({ where })
    
    // 如果有 batchSessionId，使用复合索引优化查询
    const orderByClause: any = filters.batchSessionId 
      ? [
          { batchSessionId: sortOrder },
          { batchIndex: 'asc' }
        ]
      : { [sortBy]: sortOrder }
    
    // 然后获取数据，避免同时进行大量排序操作
    const notes = await prisma.batchGeneratedNote.findMany({
      where,
      orderBy: orderByClause,
      skip,
      take: limit
    })

    return {
      data: notes,
      count: total,
      page,
      limit
    }
  }

  // Get single batch generated note
  async getBatchGeneratedNote(id: string, userId?: string) {
    const where: Prisma.BatchGeneratedNoteWhereUniqueInput = { id }
    
    return await prisma.batchGeneratedNote.findFirst({
      where: userId ? { id, userId } : { id }
    })
  }

  // Update batch generated note
  async updateBatchGeneratedNote(id: string, userId: string, data: UpdateBatchGeneratedNoteData) {
    return await prisma.batchGeneratedNote.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    })
  }

  // Update publish status
  async updatePublishStatus(id: string, userId: string, isAddedToPublish: boolean, publishedNoteId?: string) {
    return await prisma.batchGeneratedNote.update({
      where: { id },
      data: {
        isAddedToPublish,
        publishedNoteId,
        updatedAt: new Date()
      }
    })
  }

  // Delete batch generated note
  async deleteBatchGeneratedNote(id: string, userId: string) {
    return await prisma.batchGeneratedNote.delete({
      where: { id }
    })
  }

  // Delete batch session and all its notes
  async deleteBatchSession(sessionId: string, userId: string) {
    // First delete all notes in the session
    await prisma.batchGeneratedNote.deleteMany({
      where: { 
        batchSessionId: sessionId,
        userId 
      }
    })

    // Then delete the session
    return await prisma.batchGenerationSession.delete({
      where: { id: sessionId }
    })
  }

  // Get statistics for a user
  async getUserStatistics(userId: string) {
    const [totalNotes, totalSessions, publishedNotes] = await Promise.all([
      prisma.batchGeneratedNote.count({
        where: { userId }
      }),
      prisma.batchGenerationSession.count({
        where: { userId }
      }),
      prisma.batchGeneratedNote.count({
        where: { 
          userId,
          isAddedToPublish: true 
        }
      })
    ])

    return {
      totalNotes,
      totalSessions,
      publishedNotes,
      unpublishedNotes: totalNotes - publishedNotes
    }
  }
}

// Export singleton instance
export const batchGeneratedNotesRepository = new BatchGeneratedNotesRepository()