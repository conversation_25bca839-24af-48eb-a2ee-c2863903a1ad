# 舆情分析功能使用指南

## 功能概述

舆情分析功能是一个强大的工具，用于分析小红书笔记和评论的情绪倾向，帮助您更好地了解用户对特定话题的态度和反馈。

## 主要功能

### 1. 关键词搜索
- 输入关键词搜索相关笔记
- 支持标题和内容的模糊匹配
- 保存搜索历史，方便快速访问

### 2. 数据统计分析
- **笔记总数**：符合关键词的笔记数量
- **总点赞数**：所有相关笔记的点赞总和
- **总评论数**：所有相关笔记的评论总和
- **总收藏数**：所有相关笔记的收藏总和

### 3. 情绪分析

#### 笔记情绪分析
- 将笔记内容分类为：正面、负面、中性
- 显示各类情绪的分布比例
- 提供详细的笔记列表

#### 评论情绪分析
- 分析笔记评论的情绪倾向
- 分类展示正面、负面、中性评论
- 帮助了解用户真实反馈

### 4. 数据可视化

- **情绪分布图**：饼图和柱状图展示情绪分布
- **热门笔记排行**：按点赞数展示Top 10笔记
- **关键词云**：可视化展示高频关键词
- **趋势分析图**：展示最近7天的舆情变化趋势

### 5. AI智能报告
- 基于分析数据自动生成专业报告
- 包含舆情概况、趋势分析、关键发现等
- 提供实用的营销建议

## 使用步骤

### 1. 执行数据库迁移
首先需要创建必要的数据表：

```bash
# 执行SQL脚本创建表
mysql -u your_username -p your_database < sql/create-sentiment-analysis-tables.sql

# 或者使用Prisma推送
npm run db:push
```

### 2. 开始使用

1. 进入系统后，点击左侧菜单的"舆情分析"
2. 在搜索框输入想要分析的关键词
3. 点击"搜索分析"开始分析
4. 等待分析完成（首次分析可能需要几分钟）
5. 查看各项分析结果

### 3. 查看分析结果

- **总览**：快速了解整体情况
- **情绪分析**：详细的情绪分布数据
- **关键词云**：发现用户关注的热点词汇
- **趋势分析**：了解舆情的时间变化
- **AI报告**：获取专业的分析总结

### 4. 重新分析

如果需要更新数据，可以点击"重新分析"按钮，系统会重新抓取最新数据进行分析。

## 技术说明

### 缓存机制
- 分析结果会缓存24小时
- AI报告会缓存12小时
- 可以手动刷新获取最新数据

### 性能优化
- 批量处理情绪分析，避免API超时
- 异步处理分析任务，提升用户体验
- 索引优化，加快查询速度

### API限制
- 依赖用户配置的AI模型进行情绪分析
- 建议使用高性能模型以获得更准确的结果

## 注意事项

1. **数据准确性**：情绪分析基于AI模型，可能存在一定误差
2. **处理时间**：首次分析可能需要较长时间，请耐心等待
3. **数据隐私**：分析结果仅对当前用户可见
4. **配额限制**：AI分析会消耗用户的AI使用配额

## 常见问题

### Q: 分析一直显示"处理中"怎么办？
A: 可能是数据量较大，请稍等几分钟。如果长时间未完成，可以刷新页面重试。

### Q: 为什么有些笔记显示为"中性"？
A: 当AI无法明确判断情绪倾向时，会归类为中性。这通常发生在内容较短或表达模糊的情况下。

### Q: 可以导出分析报告吗？
A: 目前暂不支持导出功能，但您可以复制AI生成的报告内容。

## 更新日志

### v1.0.0 (2024-01-07)
- 初始版本发布
- 支持关键词搜索和情绪分析
- 提供多维度数据可视化
- 集成AI智能报告生成