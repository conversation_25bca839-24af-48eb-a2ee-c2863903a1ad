# 图片处理优化：固定画布尺寸

## 主要改进

### 1. 固定画布尺寸 (800x800)
- **之前**：画布尺寸根据原图动态调整，导致文字大小在不同图片上表现不一致
- **现在**：使用固定的800x800画布，确保所有图片输出尺寸一致

### 2. 图片缩放逻辑
- **之前**：画布适应图片尺寸
- **现在**：图片缩放以覆盖整个画布，保持宽高比
```javascript
const scale = Math.max(CANVAS_SIZE / img.width, CANVAS_SIZE / img.height)
const imgX = (CANVAS_SIZE / 2) - (img.width / 2) * scale
const imgY = (CANVAS_SIZE / 2) - (img.height / 2) * scale
ctx.drawImage(img, imgX, imgY, img.width * scale, img.height * scale)
```

### 3. 简化文字定位
- **之前**：复杂的位置计算函数
- **现在**：简单直接的相对定位
  - 中心：canvas中心点
  - 顶部：20%高度位置
  - 底部：80%高度位置
  - 左/右：20%/80%宽度位置

### 4. 统一的文字样式处理
- 移除了复杂的save/restore状态管理
- 直接设置文字样式和效果
- 确保文字大小相对于固定画布，而非原图

### 5. 优化的绘制顺序
1. 绘制缩放后的背景图片
2. 添加半透明遮罩层
3. 绘制主标题（带样式效果）
4. 绘制副标题
5. 添加装饰元素（HOT标签、图标等）
6. 绘制关键词标签

## 效果对比

### 修复前的问题
- 字体大小失真：在不同尺寸图片上文字大小不一致
- 背景错位：文字位置计算复杂，容易错位
- 输出尺寸不一：每张图片输出尺寸不同

### 修复后的效果
- 一致的输出尺寸：所有图片都是800x800
- 稳定的文字大小：文字大小相对于画布固定
- 准确的位置定位：文字始终在预期位置
- 更好的视觉效果：图片自动缩放填充画布

## 测试验证

创建了测试页面 `/test-fixed-canvas` 来验证不同场景：
- 不同位置的文字（中心、顶部、底部、角落）
- 不同的文字样式（描边、渐变、霓虹、普通）
- 不同尺寸的原始图片
- 装饰元素的显示效果