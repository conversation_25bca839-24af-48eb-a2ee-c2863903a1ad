# 批量图集生成器架构文档

## 概述
批量图集生成器已经从单一的1000+行组件重构为模块化的组件架构，便于维护和扩展。

## 文件结构

```
/app/dashboard/image-batch-generator/
├── page.tsx                    # 主页面组件（~300行）
├── page-old.tsx               # 原始备份

/components/image-batch-generator/
├── index.ts                   # 导出文件
├── image-uploader.tsx         # 图片上传组件
├── text-overlay-config.tsx    # 文字叠加配置
├── keywords-manager.tsx       # 关键词管理
├── advanced-settings.tsx      # 高级设置
├── batch-generation-results.tsx # 批量结果容器
├── notes-list.tsx            # 笔记列表（左侧）
├── note-editor.tsx           # 笔记编辑器（右侧）
└── single-gallery-result.tsx  # 单个图集结果

/lib/types/
├── image-batch-generator.ts   # 类型定义

/lib/utils/
├── image-batch-generator.ts   # 工具函数

/lib/
├── image-processor.ts         # 图片处理（未改动）
```

## 组件职责

### 1. ImageUploader
- 处理图片上传
- 图片预览和删除
- 拖拽排序功能

### 2. TextOverlayConfig
- 文字内容输入
- 位置选择
- 字体大小（含快捷按钮）
- 字体样式（常规/加粗）
- 颜色选择（文字/背景）
- 实时预览

### 3. KeywordsManager
- 添加/删除关键词
- 关键词标签展示

### 4. AdvancedSettings
- 批量模式开关
- 生成数量设置
- 自动变化开关
- 其他高级选项

### 5. BatchGenerationResults
- 左右分栏布局容器
- 协调子组件通信

### 6. NotesList
- 显示生成的笔记列表
- 处理笔记选择
- 批量下载功能

### 7. NoteEditor
- 编辑选中的笔记
- 标题/内容编辑
- 关键词管理
- 复制/下载/保存功能

### 8. SingleGalleryResult
- 显示单个图集生成结果
- 下载功能

## 扩展指南

### 添加新功能
1. 如果是配置项，添加到相应的配置组件中
2. 如果是新的生成模式，可以创建新组件
3. 复杂功能可以进一步拆分子组件

### 样式自定义
- 所有组件使用 shadcn/ui 组件库
- 样式通过 Tailwind CSS 类名控制
- 可以通过修改组件的 className 来调整样式

### 状态管理
- 当前使用 React hooks 进行状态管理
- 主要状态在 page.tsx 中维护
- 通过 props 传递给子组件
- 如需更复杂的状态管理，可以引入 Context 或状态管理库

## 性能优化建议
1. 图片处理在客户端进行，大量图片可能影响性能
2. 可以考虑添加图片压缩功能
3. 批量生成时可以添加进度条
4. 考虑使用 Web Worker 处理图片

## 未来改进方向
1. 添加更多模板样式
2. 支持自定义字体
3. 添加图片滤镜功能
4. 支持保存和加载配置预设
5. 添加撤销/重做功能
6. 优化移动端体验