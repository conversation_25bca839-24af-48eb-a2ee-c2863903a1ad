"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Search, TrendingUp, ThumbsUp, MessageSquare, Bookmark, RefreshCw, Coins } from "lucide-react"
import { SentimentStats } from "@/components/sentiment-analysis/sentiment-stats"
import { SentimentChart } from "@/components/sentiment-analysis/sentiment-chart"
import { WordCloud } from "@/components/sentiment-analysis/word-cloud"
import { TopNotesList } from "@/components/sentiment-analysis/top-notes-list"
import { AIReport } from "@/components/sentiment-analysis/ai-report"

export default function SentimentAnalysisPage() {
  const [keyword, setKeyword] = useState("")
  const [searchKeyword, setSearchKeyword] = useState("")
  const [loading, setLoading] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  const [analysisData, setAnalysisData] = useState<any>(null)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const { toast } = useToast()

  useEffect(() => {
    // 从localStorage加载搜索历史
    const history = localStorage.getItem("sentiment-search-history")
    if (history) {
      setSearchHistory(JSON.parse(history))
    }
  }, [])

  useEffect(() => {
    // 检查分析状态
    if (searchKeyword && analyzing) {
      const checkStatus = async () => {
        try {
          const res = await fetch(`/api/sentiment-analysis/status?keyword=${encodeURIComponent(searchKeyword)}`)
          const data = await res.json()
          
          if (data.exists && data.cache) {
            setAnalysisData(data.cache)
            if (data.cache.status === 'completed') {
              setAnalyzing(false)
            } else if (data.cache.status === 'failed') {
              setAnalyzing(false)
              toast({
                title: "分析失败",
                description: "舆情分析过程中出现错误，请重试",
                variant: "destructive"
              })
            }
          }
        } catch (error) {
          console.error("Error checking status:", error)
        }
      }

      const interval = setInterval(checkStatus, 3000)
      return () => clearInterval(interval)
    }
  }, [searchKeyword, analyzing, toast])

  const handleSearch = async () => {
    if (!keyword.trim()) {
      toast({
        title: "请输入关键词",
        description: "关键词不能为空",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    setSearchKeyword(keyword.trim())

    // 更新搜索历史
    const newHistory = [keyword.trim(), ...searchHistory.filter(h => h !== keyword.trim())].slice(0, 10)
    setSearchHistory(newHistory)
    localStorage.setItem("sentiment-search-history", JSON.stringify(newHistory))

    try {
      // 开始分析
      const analyzeRes = await fetch("/api/sentiment-analysis/analyze", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ keyword: keyword.trim() })
      })

      const analyzeData = await analyzeRes.json()
      
      if (analyzeData.cached) {
        setAnalysisData(analyzeData.data)
      } else {
        setAnalyzing(true)
        toast({
          title: "开始分析",
          description: "正在进行舆情分析，请稍候..."
        })
      }
    } catch (error) {
      console.error("Error:", error)
      toast({
        title: "操作失败",
        description: "无法开始分析，请重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    if (!searchKeyword) return

    setLoading(true)
    try {
      const analyzeRes = await fetch("/api/sentiment-analysis/analyze", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ keyword: searchKeyword, forceRefresh: true })
      })

      const analyzeData = await analyzeRes.json()
      setAnalyzing(true)
      toast({
        title: "重新分析",
        description: "正在重新进行舆情分析，请稍候..."
      })
    } catch (error) {
      console.error("Error:", error)
      toast({
        title: "操作失败",
        description: "无法重新分析，请重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const isAnalysisComplete = analysisData?.status === 'completed'

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 搜索栏 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <TrendingUp className="h-6 w-6" />
              舆情分析
            </h1>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Coins className="h-4 w-4" />
              <span>新分析: 30积分 · 重新分析: 30积分 · AI报告: 20积分</span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Input
              placeholder="输入关键词进行舆情分析..."
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch} 
              disabled={loading || analyzing}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              搜索分析
            </Button>
            {isAnalysisComplete && (
              <Button 
                variant="outline"
                onClick={handleRefresh}
                disabled={loading || analyzing}
              >
                <RefreshCw className="h-4 w-4" />
                重新分析
              </Button>
            )}
          </div>

          {/* 搜索历史 */}
          {searchHistory.length > 0 && (
            <div className="flex flex-wrap gap-2 items-center">
              <span className="text-sm text-muted-foreground">历史搜索：</span>
              {searchHistory.map((item, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  onClick={async () => {
                    setKeyword(item)
                    // 直接执行搜索逻辑
                    setLoading(true)
                    setSearchKeyword(item)

                    // 更新搜索历史
                    const newHistory = [item, ...searchHistory.filter(h => h !== item)].slice(0, 10)
                    setSearchHistory(newHistory)
                    localStorage.setItem("sentiment-search-history", JSON.stringify(newHistory))

                    try {
                      // 开始分析
                      const analyzeRes = await fetch("/api/sentiment-analysis/analyze", {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({ keyword: item })
                      })

                      const analyzeData = await analyzeRes.json()
                      
                      if (analyzeData.cached) {
                        setAnalysisData(analyzeData.data)
                      } else {
                        setAnalyzing(true)
                        toast({
                          title: "开始分析",
                          description: "正在进行舆情分析，请稍候..."
                        })
                      }
                    } catch (error) {
                      console.error("Error:", error)
                      toast({
                        title: "操作失败",
                        description: "无法开始分析，请重试",
                        variant: "destructive"
                      })
                    } finally {
                      setLoading(false)
                    }
                  }}
                >
                  {item}
                </Button>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* 分析中提示 */}
      {analyzing && (
        <Card className="p-12">
          <div className="text-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
            <h3 className="text-lg font-semibold">正在分析中...</h3>
            <p className="text-muted-foreground">
              系统正在对相关笔记和评论进行情绪分析，这可能需要几分钟时间
            </p>
          </div>
        </Card>
      )}

      {/* 分析结果 */}
      {isAnalysisComplete && (
        <>
          {/* 统计卡片 */}
          <SentimentStats data={analysisData} />

          {/* 选项卡内容 */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">总览</TabsTrigger>
              <TabsTrigger value="sentiment">情绪分析</TabsTrigger>
              <TabsTrigger value="keywords">关键词云</TabsTrigger>
              <TabsTrigger value="report">AI报告</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <SentimentChart data={analysisData} />
                <TopNotesList data={analysisData} />
              </div>
            </TabsContent>

            <TabsContent value="sentiment" className="space-y-4">
              <SentimentChart data={analysisData} detailed />
            </TabsContent>

            <TabsContent value="keywords" className="space-y-4">
              <WordCloud data={analysisData} />
            </TabsContent>


            <TabsContent value="report" className="space-y-4">
              <AIReport cacheId={analysisData.id} />
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}