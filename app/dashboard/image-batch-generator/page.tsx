"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Wand2, <PERSON>ader2, <PERSON>rid3X3, Save, History } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { v4 as uuidv4 } from "uuid"
import { useRouter } from "next/navigation"
import { PRESET_STYLES } from "@/lib/types/image-batch-generator"

// Components
import { ImageUploader } from "@/components/image-batch-generator/image-uploader"
import { TextOverlayConfig } from "@/components/image-batch-generator/text-overlay-config"
import { KeywordsManager } from "@/components/image-batch-generator/keywords-manager"
import { AdvancedSettings } from "@/components/image-batch-generator/advanced-settings"
import { BatchGenerationResultsV2 } from "@/components/image-batch-generator/batch-generation-results-v2"
import { SingleGalleryResult } from "@/components/image-batch-generator/single-gallery-result"

// Types & Utils
import type { 
  UploadedImage, 
  TextOverlay, 
  GeneratedNote, 
  GeneratorConfig
} from "@/lib/types/image-batch-generator"
import { generateTitle, generateContent } from "@/lib/utils/image-batch-generator"
import { processImageWithOverlay, createGalleryComposite } from "@/lib/image-processor"

export default function ImageBatchGeneratorPage() {
  const router = useRouter()
  
  // 图片状态
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([])
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  
  // 生成结果
  const [isGenerating, setIsGenerating] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [generatedNotes, setGeneratedNotes] = useState<GeneratedNote[]>([])
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null)
  const [generatedGallery, setGeneratedGallery] = useState<string[] | null>(null)
  const [galleryComposite, setGalleryComposite] = useState<string | null>(null)
  
  // 文字叠加配置
  const [textOverlay, setTextOverlay] = useState<TextOverlay>({
    text: "",
    position: "center",
    fontSize: 96,
    color: "#ffffff",
    bgColor: "#000000aa",
    fontWeight: "bold",
    textStyle: "normal",
    shadowColor: "#000000",
    subText: "",
    includeDecorations: true
  })
  
  // 关键词
  const [keywords, setKeywords] = useState<string[]>(["家居", "装修", "设计", "北欧风", "软装"])
  
  // 当前选中的预设样式
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null)
  
  // 高级配置
  const [generatorConfig, setGeneratorConfig] = useState<GeneratorConfig>({
    selectedTemplate: "default",
    autoGenerateTitle: true,
    includeWatermark: false,
    imageQuality: "high",
    autoVariation: true,
    batchMode: true,
    generationCount: 10
  })

  // 预览图片
  const handlePreview = async () => {
    if (uploadedImages.length === 0) return
    
    try {
      const coverImage = uploadedImages[0]
      // 如果没有文字，使用默认的预览文字
      const previewOverlay = {
        ...textOverlay,
        text: textOverlay.text || "预览效果"
      }
      const processedBlob = await processImageWithOverlay(coverImage.file, previewOverlay, keywords)
      const previewUrl = URL.createObjectURL(processedBlob)
      setPreviewImage(previewUrl)
    } catch (error) {
      console.error("Preview error:", error)
      toast({
        variant: "destructive",
        title: "预览失败",
        description: error instanceof Error ? error.message : "无法生成预览图片"
      })
    }
  }

  // 批量生成
  const handleBatchGenerate = async () => {
    if (uploadedImages.length === 0) {
      toast({
        variant: "destructive",
        title: "请先上传图片",
        description: "至少需要上传一张图片才能生成"
      })
      return
    }

    setIsGenerating(true)
    const newNotes: GeneratedNote[] = []

    try {
      for (let i = 0; i < generatorConfig.generationCount; i++) {
        // 为每个笔记生成不同的文字叠加
        const templateText = generateTitle(i, keywords)
        
        const noteTextOverlay = generatorConfig.autoVariation ? {
          ...textOverlay,
          text: textOverlay.text || templateText,
          position: ["center", "bottom-left", "top-center", "bottom-center"][i % 4] as any,
          fontSize: textOverlay.fontSize + (i % 3) * 12 - 12,
          color: i % 2 === 0 ? textOverlay.color : "#333333",
          bgColor: i % 2 === 0 ? textOverlay.bgColor : "#ffffffcc",
          textStyle: ["normal", "outline", "gradient", "neon", "bold"][i % 5] as any
        } : {
          ...textOverlay,
          text: textOverlay.text || templateText
        }

        // 处理封面图
        const coverIndex = i % uploadedImages.length
        const coverBlob = await processImageWithOverlay(
          uploadedImages[coverIndex].file,
          noteTextOverlay,
          keywords
        )
        
        const coverUrl = URL.createObjectURL(coverBlob)
        
        // 选择图片
        const noteImages = []
        for (let j = 0; j < Math.min(4, uploadedImages.length); j++) {
          const imgIndex = (i + j) % uploadedImages.length
          noteImages.push(uploadedImages[imgIndex].preview)
        }
        
        const note: GeneratedNote = {
          id: uuidv4(),
          title: generateTitle(i, keywords),
          content: generateContent(i, keywords),
          coverImage: coverUrl,
          images: [coverUrl, ...noteImages.slice(1)],
          keywords: keywords.slice(0, 3 + (i % 3)),
          textOverlay: noteTextOverlay
        }
        
        newNotes.push(note)
      }

      setGeneratedNotes(newNotes)
      if (newNotes.length > 0) {
        setSelectedNoteId(newNotes[0].id)
      }
      
      toast({
        title: "生成成功",
        description: `成功生成 ${generatorConfig.generationCount} 个笔记，正在保存...`
      })

      // 自动保存到数据库
      await handleSaveToDatabase(newNotes)
      
    } catch (error) {
      toast({
        variant: "destructive",
        title: "生成失败",
        description: error instanceof Error ? error.message : "请稍后重试"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 生成单个图集
  const handleGenerateGallery = async () => {
    if (uploadedImages.length === 0) {
      toast({
        variant: "destructive",
        title: "请先上传图片",
        description: "至少需要上传一张图片才能生成图集"
      })
      return
    }

    setIsGenerating(true)
    
    try {
      // 处理封面图片
      let processedCoverBlob: Blob | null = null
      if (textOverlay.text && uploadedImages.length > 0) {
        processedCoverBlob = await processImageWithOverlay(uploadedImages[0].file, textOverlay, keywords)
      }

      // 创建图集组合
      const compositeBlob = await createGalleryComposite(
        uploadedImages.map(img => img.file),
        generatorConfig.selectedTemplate,
        generatorConfig.includeWatermark
      )
      
      const formData = new FormData()
      
      // 添加图片文件
      uploadedImages.forEach((img, index) => {
        formData.append(`images`, img.file)
        formData.append(`imageOrders`, img.order.toString())
      })
      
      // 如果有处理过的封面图片，添加它
      if (processedCoverBlob) {
        formData.append("processedCoverImage", processedCoverBlob, "cover.jpg")
      }
      
      // 添加配置
      formData.append("template", generatorConfig.selectedTemplate)
      formData.append("keywords", JSON.stringify(keywords))
      formData.append("autoGenerateTitle", generatorConfig.autoGenerateTitle.toString())
      formData.append("includeWatermark", generatorConfig.includeWatermark.toString())
      formData.append("imageQuality", generatorConfig.imageQuality)

      const response = await fetch("/api/image-gallery-generator", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.text()
        console.error("API Error:", errorData)
        throw new Error(`生成失败: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      setGeneratedGallery(data.galleryImages)
      
      // 设置本地生成的组合图预览
      const compositeUrl = URL.createObjectURL(compositeBlob)
      setGalleryComposite(compositeUrl)
      
      toast({
        title: "生成成功",
        description: "图集已成功生成，可以下载或分享"
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "生成失败",
        description: error instanceof Error ? error.message : "请稍后重试"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 更新笔记
  const handleUpdateNote = (noteId: string, updates: Partial<GeneratedNote>) => {
    setGeneratedNotes(prev => prev.map(note => 
      note.id === noteId ? { ...note, ...updates } : note
    ))
  }

  // 关闭批量结果
  const handleCloseBatchResults = () => {
    setGeneratedNotes([])
    setSelectedNoteId(null)
  }

  // 应用预设样式
  const applyPreset = (presetKey: string) => {
    const preset = PRESET_STYLES[presetKey]
    if (!preset) return
    
    setTextOverlay(prev => ({
      ...prev,
      text: preset.mainText,
      subText: preset.subText,
      color: preset.textColor,
      shadowColor: preset.shadowColor,
      textStyle: preset.textStyle,
      bgColor: preset.bgColor
    }))
    
    setKeywords(preset.keywords)
    setSelectedPreset(presetKey)
    
    // 触发预览
    if (uploadedImages.length > 0) {
      setTimeout(() => handlePreview(), 100)
    }
  }

  // 上传blob到R2存储
  const uploadBlobToR2 = async (blobUrl: string, filename: string, retries = 3): Promise<string> => {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await fetch(blobUrl)
        const blob = await response.blob()
        
        // 创建FormData
        const formData = new FormData()
        formData.append('file', blob, filename)
        formData.append('folder', 'batch-generator')
        
        // 上传到R2
        const uploadResponse = await fetch('/api/storage/upload', {
          method: 'POST',
          body: formData
        })
        
        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.status}`)
        }
        
        const result = await uploadResponse.json()
        return result.url
      } catch (error) {
        console.error(`Failed to upload ${filename} (attempt ${attempt}/${retries}):`, error)
        
        // 如果是最后一次尝试，返回base64
        if (attempt === retries) {
          console.warn(`All upload attempts failed for ${filename}, falling back to base64`)
          return blobToBase64(blobUrl)
        }
        
        // 等待一下再重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
    
    // 不应该到达这里，但以防万一
    return blobToBase64(blobUrl)
  }

  // 将blob URL转换为base64（作为备选方案）
  const blobToBase64 = async (blobUrl: string): Promise<string> => {
    try {
      const response = await fetch(blobUrl)
      const blob = await response.blob()
      
      // 检查文件大小，如果超过500KB，返回一个占位符
      if (blob.size > 500 * 1024) {
        console.warn(`Image too large for base64: ${blob.size} bytes`)
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      }
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result as string)
        reader.onerror = reject
        reader.readAsDataURL(blob)
      })
    } catch (error) {
      console.error('Failed to convert blob to base64:', error)
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    }
  }

  // 保存到数据库
  const handleSaveToDatabase = async (notesToSave?: GeneratedNote[]) => {
    const notes = notesToSave || generatedNotes
    
    if (notes.length === 0) {
      toast({
        variant: "destructive",
        title: "没有可保存的内容",
        description: "请先生成笔记"
      })
      return
    }

    setIsSaving(true)
    
    // 显示开始上传的提示
    const uploadToast = toast({
      title: "正在上传图片...",
      description: "准备上传图片到云存储",
      duration: Infinity // 不自动关闭
    })
    
    try {
      // 准备保存的数据
      const sessionName = `批量生成_${new Date().toLocaleDateString('zh-CN')}_${new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
      
      // 收集所有需要上传的图片
      const timestamp = Date.now()
      const uploadTasks: Array<{
        noteIndex: number
        type: 'cover' | 'content'
        imageIndex: number
        blobUrl: string
        filename: string
      }> = []

      // 收集所有图片上传任务
      notes.forEach((note, noteIndex) => {
        // 封面图片
        if (note.coverImage.startsWith('blob:')) {
          uploadTasks.push({
            noteIndex,
            type: 'cover',
            imageIndex: 0,
            blobUrl: note.coverImage,
            filename: `cover_${timestamp}_${noteIndex}.jpg`
          })
        }

        // 内容图片
        note.images.forEach((img, imgIndex) => {
          if (img.startsWith('blob:')) {
            uploadTasks.push({
              noteIndex,
              type: 'content',
              imageIndex: imgIndex,
              blobUrl: img,
              filename: `content_${timestamp}_${noteIndex}_${imgIndex}.jpg`
            })
          }
        })
      })

      console.log(`Starting upload of ${uploadTasks.length} images...`)

      // 批量并发上传
      const uploadResults = new Map<string, string>()
      let uploadedCount = 0
      
      // 使用并发限制器
      const concurrentLimit = 10 // 同时上传的最大数量
      let currentIndex = 0
      
      // 创建上传队列处理器
      const uploadQueue = async () => {
        while (currentIndex < uploadTasks.length) {
          const taskIndex = currentIndex++
          const task = uploadTasks[taskIndex]
          
          try {
            const url = await uploadBlobToR2(task.blobUrl, task.filename)
            uploadResults.set(task.blobUrl, url)
            uploadedCount++
            
            // 更新上传进度
            uploadToast.update({
              id: uploadToast.id,
              title: "正在上传图片...",
              description: `已上传 ${uploadedCount}/${uploadTasks.length} 张图片 (${Math.round(uploadedCount / uploadTasks.length * 100)}%)`,
              duration: Infinity
            })
            
            console.log(`Uploaded ${task.filename} (${uploadedCount}/${uploadTasks.length})`)
          } catch (error) {
            console.error(`Failed to upload ${task.filename}:`, error)
            // 如果上传失败，使用base64作为备选
            const base64 = await blobToBase64(task.blobUrl)
            uploadResults.set(task.blobUrl, base64)
            uploadedCount++
          }
        }
      }
      
      // 启动并发上传
      const uploaders = Array(Math.min(concurrentLimit, uploadTasks.length))
        .fill(null)
        .map(() => uploadQueue())
      
      await Promise.all(uploaders)

      console.log('All images uploaded successfully')
      
      // 关闭上传进度提示
      uploadToast.dismiss()

      // 构建笔记数据，使用上传后的URL
      const notesData = notes.map((note, index) => ({
        title: note.title,
        content: note.content,
        coverImage: uploadResults.get(note.coverImage) || note.coverImage,
        contentImages: note.images.map(img => uploadResults.get(img) || img),
        keywords: note.keywords,
        templateSettings: {
          template: generatorConfig.selectedTemplate,
          autoGenerateTitle: generatorConfig.autoGenerateTitle,
          includeWatermark: generatorConfig.includeWatermark
        },
        textOverlayConfig: note.textOverlay,
        generationConfig: {
          imageQuality: generatorConfig.imageQuality,
          autoVariation: generatorConfig.autoVariation,
          batchMode: generatorConfig.batchMode,
          generationCount: generatorConfig.generationCount
        }
      }))

      const response = await fetch('/api/image-batch-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionName,
          notes: notesData
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Save failed:', errorData)
        throw new Error(errorData.error || '保存失败')
      }

      const result = await response.json()
      
      toast({
        title: "保存成功",
        description: `成功保存 ${result.notesCreated} 个笔记到数据库`
      })

      // 如果不是自动保存（手动保存），清空当前生成的笔记
      if (!notesToSave) {
        setGeneratedNotes([])
        setSelectedNoteId(null)
      }
      
    } catch (error) {
      toast({
        variant: "destructive",
        title: "保存失败",
        description: error instanceof Error ? error.message : "请稍后重试"
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">批量图集生成</h1>
          <p className="text-muted-foreground mt-2">
            上传多张图片，快速生成小红书风格的图集，支持添加文字花字和关键词
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push('/dashboard/image-batch-generator/history')}
        >
          <History className="mr-2 h-4 w-4" />
          历史生成记录
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：图片上传和管理 */}
        <div className="lg:col-span-2 space-y-6">
          <ImageUploader 
            uploadedImages={uploadedImages} 
            onImagesChange={setUploadedImages} 
          />

          {/* 生成结果预览 */}
          {galleryComposite && !generatorConfig.batchMode && (
            <SingleGalleryResult
              galleryComposite={galleryComposite}
              generatedGallery={generatedGallery}
            />
          )}
        </div>

        {/* 右侧：配置选项 */}
        <div className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">图集配置</h2>
            
            <Tabs defaultValue="text" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="text">文字花字</TabsTrigger>
                <TabsTrigger value="keywords">关键词</TabsTrigger>
                <TabsTrigger value="advanced">高级</TabsTrigger>
              </TabsList>
              
              <TabsContent value="text" className="space-y-4 mt-4">
                <TextOverlayConfig
                  textOverlay={textOverlay}
                  onTextOverlayChange={setTextOverlay}
                  uploadedImages={uploadedImages}
                  previewImage={previewImage}
                  onPreview={handlePreview}
                  batchMode={generatorConfig.batchMode}
                  autoVariation={generatorConfig.autoVariation}
                  onApplyPreset={applyPreset}
                  selectedPreset={selectedPreset}
                />
              </TabsContent>
              
              <TabsContent value="keywords" className="space-y-4 mt-4">
                <KeywordsManager
                  keywords={keywords}
                  onKeywordsChange={setKeywords}
                />
              </TabsContent>
              
              <TabsContent value="advanced" className="space-y-4 mt-4">
                <AdvancedSettings
                  config={generatorConfig}
                  onConfigChange={(updates) => 
                    setGeneratorConfig(prev => ({ ...prev, ...updates }))
                  }
                />
              </TabsContent>
            </Tabs>
          </Card>

          <Button
            className="w-full"
            size="lg"
            onClick={generatorConfig.batchMode ? handleBatchGenerate : handleGenerateGallery}
            disabled={uploadedImages.length === 0 || isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                {generatorConfig.batchMode ? (
                  <>
                    <Grid3X3 className="mr-2 h-4 w-4" />
                    批量生成 {generatorConfig.generationCount} 个笔记
                  </>
                ) : (
                  <>
                    <Wand2 className="mr-2 h-4 w-4" />
                    生成图集
                  </>
                )}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 批量生成结果 */}
      {generatorConfig.batchMode && generatedNotes.length > 0 && (
        <BatchGenerationResultsV2
          notes={generatedNotes}
          selectedNoteId={selectedNoteId}
          onSelectNote={setSelectedNoteId}
          onClose={handleCloseBatchResults}
          onUpdateNote={handleUpdateNote}
          onSaveToDatabase={handleSaveToDatabase}
          isSaving={isSaving}
        />
      )}
    </div>
  )
}