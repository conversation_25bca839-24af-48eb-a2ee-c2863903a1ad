"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  ArrowLeft, 
  Search, 
  Calendar,
  Image,
  FileText,
  Eye,
  Trash2,
  Loader2,
  CheckCircle2
} from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { cn } from "@/lib/utils"

interface BatchSession {
  id: string
  sessionName: string
  totalGenerated: number
  createdAt: string
  metadata?: any
}

interface BatchGeneratedNote {
  id: string
  title: string
  content: string
  coverImage: string
  contentImages: any[]
  keywords: string[]
  isAddedToPublish: boolean
  publishedNoteId?: string
  createdAt: string
}

export default function BatchGeneratorHistoryPage() {
  const router = useRouter()
  const [sessions, setSessions] = useState<BatchSession[]>([])
  const [selectedSession, setSelectedSession] = useState<BatchSession | null>(null)
  const [sessionNotes, setSessionNotes] = useState<BatchGeneratedNote[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingNotes, setIsLoadingNotes] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedNote, setSelectedNote] = useState<BatchGeneratedNote | null>(null)

  // 加载历史会话
  useEffect(() => {
    loadSessions()
  }, [])

  const loadSessions = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/image-batch-generator/sessions')
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Failed to load sessions:', errorData)
        throw new Error(errorData.error || '加载失败')
      }
      
      const data = await response.json()
      console.log('Sessions data:', data)
      setSessions(data.data || [])
    } catch (error) {
      console.error('Error loading sessions:', error)
      toast({
        variant: "destructive",
        title: "加载失败",
        description: error instanceof Error ? error.message : "无法加载历史记录"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 加载会话的笔记
  const loadSessionNotes = async (sessionId: string) => {
    try {
      setIsLoadingNotes(true)
      const response = await fetch(`/api/image-batch-generator?batchSessionId=${sessionId}`)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Failed to load notes:', errorData)
        throw new Error(errorData.error || '加载失败')
      }
      
      const data = await response.json()
      console.log('Notes data:', data)
      setSessionNotes(data.data || [])
    } catch (error) {
      console.error('Error loading notes:', error)
      toast({
        variant: "destructive",
        title: "加载失败",
        description: error instanceof Error ? error.message : "无法加载笔记内容"
      })
    } finally {
      setIsLoadingNotes(false)
    }
  }

  // 选择会话
  const handleSelectSession = (session: BatchSession) => {
    setSelectedSession(session)
    loadSessionNotes(session.id)
  }

  // 更新发布状态
  const handleTogglePublishStatus = async (noteId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/image-batch-generator/${noteId}/publish-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isAddedToPublish: !currentStatus
        })
      })

      if (!response.ok) throw new Error('更新失败')

      // 更新本地状态
      setSessionNotes(prev => prev.map(note => 
        note.id === noteId 
          ? { ...note, isAddedToPublish: !currentStatus }
          : note
      ))

      toast({
        title: "更新成功",
        description: !currentStatus ? "已添加到待发布" : "已从待发布移除"
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "更新失败",
        description: "无法更新发布状态"
      })
    }
  }

  // 删除笔记
  const handleDeleteNote = async (noteId: string) => {
    if (!confirm("确定要删除这个笔记吗？")) return

    try {
      const response = await fetch(`/api/image-batch-generator/${noteId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('删除失败')

      // 更新本地状态
      setSessionNotes(prev => prev.filter(note => note.id !== noteId))

      toast({
        title: "删除成功",
        description: "笔记已删除"
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "删除失败",
        description: "无法删除笔记"
      })
    }
  }

  // 过滤会话
  const filteredSessions = sessions.filter(session =>
    session.sessionName.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <h1 className="text-2xl font-bold">历史生成记录</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：会话列表 */}
        <div className="lg:col-span-1 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索会话..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : filteredSessions.length === 0 ? (
            <Card className="p-6 text-center text-muted-foreground">
              暂无历史记录
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredSessions.map((session) => (
                <Card
                  key={session.id}
                  className={cn(
                    "p-4 cursor-pointer transition-all hover:shadow-md",
                    selectedSession?.id === session.id && "ring-2 ring-primary"
                  )}
                  onClick={() => handleSelectSession(session)}
                >
                  <h3 className="font-medium mb-2">{session.sessionName}</h3>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {format(new Date(session.createdAt), 'MM月dd日 HH:mm', { locale: zhCN })}
                    </div>
                    <div className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      {session.totalGenerated} 个笔记
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* 右侧：笔记列表 */}
        <div className="lg:col-span-2">
          {!selectedSession ? (
            <Card className="p-12 text-center text-muted-foreground">
              <Image className="h-12 w-12 mx-auto mb-4 opacity-20" />
              <p>选择一个会话查看生成的笔记</p>
            </Card>
          ) : isLoadingNotes ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">
                  {selectedSession.sessionName} 的笔记
                </h2>
                <Badge variant="secondary">
                  共 {sessionNotes.length} 个
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sessionNotes.map((note) => (
                  <Card key={note.id} className="overflow-hidden">
                    <div className="relative aspect-[3/4]">
                      <img
                        src={note.coverImage}
                        alt={note.title}
                        className="w-full h-full object-cover"
                      />
                      {note.isAddedToPublish && (
                        <Badge className="absolute top-2 right-2">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          待发布
                        </Badge>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-medium line-clamp-2 mb-2">{note.title}</h3>
                      <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                        {note.content}
                      </p>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {note.keywords.map((keyword, idx) => (
                          <Badge key={idx} variant="secondary" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedNote(note)}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          查看
                        </Button>
                        <Button
                          size="sm"
                          variant={note.isAddedToPublish ? "secondary" : "default"}
                          onClick={() => handleTogglePublishStatus(note.id, note.isAddedToPublish)}
                        >
                          {note.isAddedToPublish ? "移除待发布" : "添加到待发布"}
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDeleteNote(note.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 查看笔记详情对话框 */}
      <Dialog open={!!selectedNote} onOpenChange={(open) => !open && setSelectedNote(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedNote?.title}</DialogTitle>
            <DialogDescription>
              创建于 {selectedNote && format(new Date(selectedNote.createdAt), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
            </DialogDescription>
          </DialogHeader>
          
          {selectedNote && (
            <div className="space-y-6">
              <div>
                <h3 className="font-medium mb-2">封面图片</h3>
                <img
                  src={selectedNote.coverImage}
                  alt="封面"
                  className="w-full max-w-md mx-auto rounded-lg"
                />
              </div>
              
              <div>
                <h3 className="font-medium mb-2">内容</h3>
                <p className="whitespace-pre-wrap text-sm">{selectedNote.content}</p>
              </div>

              {selectedNote.contentImages.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">图片集 ({selectedNote.contentImages.length}张)</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {selectedNote.contentImages.map((img, idx) => (
                      <img
                        key={idx}
                        src={img}
                        alt={`图片${idx + 1}`}
                        className="w-full aspect-square object-cover rounded"
                      />
                    ))}
                  </div>
                </div>
              )}

              <div>
                <h3 className="font-medium mb-2">关键词</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedNote.keywords.map((keyword, idx) => (
                    <Badge key={idx} variant="secondary">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}