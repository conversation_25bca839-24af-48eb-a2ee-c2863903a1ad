"use client"

import { useState, useRef } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Upload, X, Download, Wand2, Loader2, Plus, Eye, Grid3X3, <PERSON><PERSON>, Save, FileText } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import { processImageWithOverlay, createGalleryComposite } from "@/lib/image-processor"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { v4 as uuidv4 } from "uuid"

interface UploadedImage {
  id: string
  file: File
  preview: string
  order: number
}

interface TextOverlay {
  text: string
  position: "top-left" | "top-center" | "top-right" | "center" | "bottom-left" | "bottom-center" | "bottom-right"
  fontSize: number
  color: string
  bgColor: string
  fontWeight: "normal" | "bold"
}

interface GeneratedNote {
  id: string
  title: string
  content: string
  coverImage: string
  images: string[]
  keywords: string[]
  textOverlay: TextOverlay
}

export default function ImageBatchGeneratorPage() {
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState("default")
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedGallery, setGeneratedGallery] = useState<string[] | null>(null)
  const [galleryComposite, setGalleryComposite] = useState<string | null>(null)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // 批量生成
  const [batchMode, setBatchMode] = useState(true) // 默认开启批量模式
  const [generationCount, setGenerationCount] = useState(10)
  const [generatedNotes, setGeneratedNotes] = useState<GeneratedNote[]>([])
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null)
  
  // 文字叠加配置
  const [textOverlay, setTextOverlay] = useState<TextOverlay>({
    text: "",
    position: "center",
    fontSize: 96, // 增大默认字体大小
    color: "#ffffff",
    bgColor: "#000000aa",
    fontWeight: "bold"
  })
  
  // 关键词配置
  const [keywords, setKeywords] = useState<string[]>(["家居", "装修", "设计", "北欧风", "软装"])
  const [currentKeyword, setCurrentKeyword] = useState("")
  
  // 高级设置
  const [autoGenerateTitle, setAutoGenerateTitle] = useState(true)
  const [includeWatermark, setIncludeWatermark] = useState(false)
  const [imageQuality, setImageQuality] = useState("high")
  const [autoVariation, setAutoVariation] = useState(true)

  // 获取选中的笔记
  const selectedNote = generatedNotes.find(note => note.id === selectedNoteId)

  // 更新选中笔记的内容
  const updateSelectedNote = (updates: Partial<GeneratedNote>) => {
    if (!selectedNoteId) return
    
    setGeneratedNotes(prev => prev.map(note => 
      note.id === selectedNoteId 
        ? { ...note, ...updates }
        : note
    ))
  }

  // 为选中笔记添加关键词
  const addKeywordToNote = (keyword: string) => {
    if (!selectedNote) return
    updateSelectedNote({ 
      keywords: [...selectedNote.keywords, keyword]
    })
  }

  // 从选中笔记移除关键词
  const removeKeywordFromNote = (index: number) => {
    if (!selectedNote) return
    updateSelectedNote({
      keywords: selectedNote.keywords.filter((_, i) => i !== index)
    })
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files) return

    const newImages: UploadedImage[] = []
    Array.from(files).forEach((file, index) => {
      const reader = new FileReader()
      reader.onloadend = () => {
        newImages.push({
          id: Date.now().toString() + index,
          file,
          preview: reader.result as string,
          order: uploadedImages.length + index
        })
        
        if (newImages.length === files.length) {
          setUploadedImages(prev => [...prev, ...newImages])
        }
      }
      reader.readAsDataURL(file)
    })
  }

  const removeImage = (id: string) => {
    setUploadedImages(prev => prev.filter(img => img.id !== id))
  }

  const reorderImages = (draggedId: string, targetId: string) => {
    const draggedIndex = uploadedImages.findIndex(img => img.id === draggedId)
    const targetIndex = uploadedImages.findIndex(img => img.id === targetId)
    
    if (draggedIndex === -1 || targetIndex === -1) return

    const newImages = [...uploadedImages]
    const [draggedImage] = newImages.splice(draggedIndex, 1)
    newImages.splice(targetIndex, 0, draggedImage)
    
    setUploadedImages(newImages.map((img, index) => ({ ...img, order: index })))
  }

  const addKeyword = () => {
    if (currentKeyword.trim() && keywords.length < 10) {
      setKeywords(prev => [...prev, currentKeyword.trim()])
      setCurrentKeyword("")
    }
  }

  const removeKeyword = (index: number) => {
    setKeywords(prev => prev.filter((_, i) => i !== index))
  }

  const handlePreview = async () => {
    if (uploadedImages.length === 0 || !textOverlay.text) return
    
    try {
      const coverImage = uploadedImages[0]
      const processedBlob = await processImageWithOverlay(coverImage.file, textOverlay, keywords)
      const previewUrl = URL.createObjectURL(processedBlob)
      setPreviewImage(previewUrl)
    } catch (error) {
      toast({
        variant: "destructive",
        title: "预览失败",
        description: "无法生成预览图片"
      })
    }
  }

  // 标题模板
  const TITLE_TEMPLATES = [
    "把{keyword}搬进家啦～",
    "{keyword}｜{style}风格推荐",
    "新家装修好了，分享一下{keyword}",
    "{keyword}设计｜{style}奶油风",
    "北欧风{keyword}案例分享",
    "{keyword}布置分享｜{style}",
    "一镜到底看{keyword}",
    "{style}风{keyword}效果图",
    "花了{price}装出{keyword}",
    "{keyword}改造｜before&after"
  ]

  const CONTENT_TEMPLATES = [
    `新家装修好了也有一段时间了，今天周末正好有空分享一下，{keyword}@家居薯

这个家是{style}的设计，整体配色以{color}为主。白色是北欧风设计中的重要元素，空间中的主要家具也是以白色和奶油色为主，所以整体感觉还是组合得很和谐的。家具多采用自然元素，搭配米色、灰色、绿色等浅色，北欧装修风格强调简洁，色彩和线条，整个家居风格很简洁舒适。`,
    
    `{keyword}终于完工啦！分享一下装修心得～

🏠 房屋信息：{area}平米 | {style}风格
💰 装修预算：{budget}万（硬装+软装）
⏰ 装修时长：{duration}个月

✨ 设计亮点：
1. {highlight1}
2. {highlight2}
3. {highlight3}

📍 材料清单：
- 地板：{material1}
- 墙面：{material2}
- 家具：{material3}`,

    `{keyword}分享 | {style}风格的温馨小家

最近刚搬进新家，迫不及待想要分享给大家！这次装修主打的就是{style}风格，整体色调以{color}为主。

客厅部分选择了{furniture1}，搭配{furniture2}，营造出温馨舒适的氛围。`
  ]

  const generateTitle = (index: number) => {
    const template = TITLE_TEMPLATES[index % TITLE_TEMPLATES.length]
    const keyword = keywords[index % keywords.length] || "家居"
    const style = ["北欧", "现代", "简约", "奶油", "日式"][index % 5]
    const price = ["3万", "5万", "8万", "10万", "15万"][index % 5]
    
    return template
      .replace("{keyword}", keyword)
      .replace("{style}", style)
      .replace("{price}", price)
  }

  const generateContent = (index: number) => {
    const template = CONTENT_TEMPLATES[index % CONTENT_TEMPLATES.length]
    const keyword = keywords[index % keywords.length] || "家居"
    const style = ["北欧风", "现代简约", "奶油风", "日式", "轻奢"][index % 5]
    const color = ["白色和原木色", "灰白色调", "米色系", "暖色调", "莫兰迪色"][index % 5]
    const area = [60, 80, 90, 110, 120][index % 5]
    const budget = [10, 15, 20, 25, 30][index % 5]
    const duration = [2, 3, 4, 5, 6][index % 5]
    
    return template
      .replace("{keyword}", keyword)
      .replace("{style}", style)
      .replace("{color}", color)
      .replace("{area}", area.toString())
      .replace("{budget}", budget.toString())
      .replace("{duration}", duration.toString())
      .replace("{highlight1}", "开放式厨房设计")
      .replace("{highlight2}", "整面收纳墙")
      .replace("{highlight3}", "无主灯设计")
      .replace("{material1}", "进口强化地板")
      .replace("{material2}", "乳胶漆+护墙板")
      .replace("{material3}", "宜家+网购")
      .replace("{furniture1}", "布艺沙发")
      .replace("{furniture2}", "实木茶几")
  }

  const handleBatchGenerate = async () => {
    if (uploadedImages.length === 0) {
      toast({
        variant: "destructive",
        title: "请先上传图片",
        description: "至少需要上传一张图片才能生成"
      })
      return
    }

    setIsGenerating(true)
    const newNotes: GeneratedNote[] = []

    try {
      for (let i = 0; i < generationCount; i++) {
        // 为每个笔记生成不同的文字叠加
        const templateText = TITLE_TEMPLATES[i % TITLE_TEMPLATES.length]
          .replace("{keyword}", keywords[0] || "家居")
          .replace("{style}", ["北欧", "现代", "简约", "奶油", "日式"][i % 5])
          .replace("{price}", ["3万", "5万", "8万", "10万", "15万"][i % 5])
        
        const noteTextOverlay = autoVariation ? {
          ...textOverlay,
          text: textOverlay.text || templateText,
          position: ["center", "bottom-left", "top-center", "bottom-center"][i % 4] as any,
          fontSize: textOverlay.fontSize + (i % 3) * 12 - 12, // 增大字体变化幅度
          color: i % 2 === 0 ? textOverlay.color : "#333333",
          bgColor: i % 2 === 0 ? textOverlay.bgColor : "#ffffffcc"
        } : {
          ...textOverlay,
          text: textOverlay.text || templateText // 即使关闭自动变化，也要确保有文字
        }
        

        // 处理封面图
        const coverIndex = i % uploadedImages.length
        const coverBlob = await processImageWithOverlay(
          uploadedImages[coverIndex].file,
          noteTextOverlay,
          keywords
        )
        
        const coverUrl = URL.createObjectURL(coverBlob)
        
        // 选择图片（每个笔记使用不同的图片组合）
        const noteImages = []
        for (let j = 0; j < Math.min(4, uploadedImages.length); j++) {
          const imgIndex = (i + j) % uploadedImages.length
          noteImages.push(uploadedImages[imgIndex].preview)
        }
        
        const note: GeneratedNote = {
          id: uuidv4(),
          title: generateTitle(i),
          content: generateContent(i),
          coverImage: coverUrl,
          images: [coverUrl, ...noteImages.slice(1)],
          keywords: keywords.slice(0, 3 + (i % 3)),
          textOverlay: noteTextOverlay
        }
        
        newNotes.push(note)
      }

      setGeneratedNotes(newNotes)
      setBatchMode(true)
      // 自动选中第一个笔记
      if (newNotes.length > 0) {
        setSelectedNoteId(newNotes[0].id)
      }
      
      toast({
        title: "生成成功",
        description: `成功生成 ${generationCount} 个笔记`
      })
      
    } catch (error) {
      toast({
        variant: "destructive",
        title: "生成失败",
        description: error instanceof Error ? error.message : "请稍后重试"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleGenerateGallery = async () => {
    if (uploadedImages.length === 0) {
      toast({
        variant: "destructive",
        title: "请先上传图片",
        description: "至少需要上传一张图片才能生成图集"
      })
      return
    }

    setIsGenerating(true)
    
    try {
      // 处理封面图片（如果有文字叠加）
      let processedCoverBlob: Blob | null = null
      if (textOverlay.text && uploadedImages.length > 0) {
        processedCoverBlob = await processImageWithOverlay(uploadedImages[0].file, textOverlay, keywords)
      }

      // 创建图集组合
      const compositeBlob = await createGalleryComposite(
        uploadedImages.map(img => img.file),
        selectedTemplate,
        includeWatermark
      )
      
      const formData = new FormData()
      
      // 添加图片文件
      uploadedImages.forEach((img, index) => {
        formData.append(`images`, img.file)
        formData.append(`imageOrders`, img.order.toString())
      })
      
      // 如果有处理过的封面图片，添加它
      if (processedCoverBlob) {
        formData.append("processedCoverImage", processedCoverBlob, "cover.jpg")
      }
      
      // 添加配置
      formData.append("template", selectedTemplate)
      formData.append("keywords", JSON.stringify(keywords))
      formData.append("autoGenerateTitle", autoGenerateTitle.toString())
      formData.append("includeWatermark", includeWatermark.toString())
      formData.append("imageQuality", imageQuality)

      const response = await fetch("/api/image-gallery-generator", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.text()
        console.error("API Error:", errorData)
        throw new Error(`生成失败: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      setGeneratedGallery(data.galleryImages)
      
      // 设置本地生成的组合图预览
      const compositeUrl = URL.createObjectURL(compositeBlob)
      setGalleryComposite(compositeUrl)
      
      toast({
        title: "生成成功",
        description: "图集已成功生成，可以下载或分享"
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "生成失败",
        description: error instanceof Error ? error.message : "请稍后重试"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">批量图集生成</h1>
        <p className="text-muted-foreground mt-2">
          上传多张图片，快速生成小红书风格的图集，支持添加文字花字和关键词
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：图片上传和管理 */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">上传图片</h2>
            
            <div className="space-y-4">
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-sm text-muted-foreground">
                  点击或拖拽图片到此处上传
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  支持 JPG、PNG、GIF 格式，单张最大 10MB
                </p>
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />

              {/* 已上传图片列表 */}
              {uploadedImages.length > 0 && (
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4">
                  {uploadedImages
                    .sort((a, b) => a.order - b.order)
                    .map((image, index) => (
                      <div
                        key={image.id}
                        className="relative group aspect-square"
                        draggable
                        onDragStart={(e) => {
                          e.dataTransfer.setData("imageId", image.id)
                        }}
                        onDragOver={(e) => e.preventDefault()}
                        onDrop={(e) => {
                          e.preventDefault()
                          const draggedId = e.dataTransfer.getData("imageId")
                          reorderImages(draggedId, image.id)
                        }}
                      >
                        <img
                          src={image.preview}
                          alt={`上传的图片 ${index + 1}`}
                          className="w-full h-full object-cover rounded-lg"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                          <Button
                            size="icon"
                            variant="ghost"
                            className="text-white hover:bg-white/20"
                            onClick={() => removeImage(image.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        {index === 0 && (
                          <div className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                            封面
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              )}
            </div>
          </Card>

          {/* 生成结果预览 */}
          {galleryComposite && !batchMode && (
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">生成结果</h2>
                <div className="flex gap-2">
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      const link = document.createElement('a')
                      link.href = galleryComposite
                      link.download = `gallery_${Date.now()}.jpg`
                      link.click()
                    }}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    下载组合图
                  </Button>
                </div>
              </div>
              <div className="aspect-[4/3] bg-muted rounded-lg overflow-hidden">
                <img
                  src={galleryComposite}
                  alt="生成的图集"
                  className="w-full h-full object-contain"
                />
              </div>
              
              {/* 单独显示所有图片 */}
              {generatedGallery && generatedGallery.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium mb-2">单独图片</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {generatedGallery.map((url, index) => (
                      <div key={index} className="aspect-square bg-muted rounded overflow-hidden">
                        <img src={url} alt={`图片 ${index + 1}`} className="w-full h-full object-cover" />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </Card>
          )}
        </div>

        {/* 右侧：配置选项 */}
        <div className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">图集配置</h2>
            
            <Tabs defaultValue="text" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="text">文字花字</TabsTrigger>
                <TabsTrigger value="keywords">关键词</TabsTrigger>
                <TabsTrigger value="advanced">高级</TabsTrigger>
              </TabsList>
              
              <TabsContent value="text" className="space-y-4 mt-4">
                <div>
                  <Label htmlFor="overlay-text">花字文本</Label>
                  <Textarea
                    id="overlay-text"
                    placeholder="输入要显示在封面上的文字（留空则自动生成）"
                    value={textOverlay.text}
                    onChange={(e) => setTextOverlay(prev => ({ ...prev, text: e.target.value }))}
                    className="mt-1"
                  />
                  {batchMode && autoVariation && !textOverlay.text && (
                    <p className="text-xs text-muted-foreground mt-1">
                      批量模式下将自动为每个笔记生成不同的花字文本
                    </p>
                  )}
                  {uploadedImages.length > 0 && textOverlay.text && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="mt-2"
                      onClick={handlePreview}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      预览效果
                    </Button>
                  )}
                </div>
                
                {/* 预览图片 */}
                {previewImage && (
                  <div className="mt-4">
                    <Label>预览效果</Label>
                    <div className="mt-2 aspect-square bg-muted rounded-lg overflow-hidden">
                      <img
                        src={previewImage}
                        alt="预览"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )}
                
                <div>
                  <Label htmlFor="text-position">位置</Label>
                  <Select
                    value={textOverlay.position}
                    onValueChange={(value: any) => setTextOverlay(prev => ({ ...prev, position: value }))}
                  >
                    <SelectTrigger id="text-position" className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="top-left">左上</SelectItem>
                      <SelectItem value="top-center">顶部居中</SelectItem>
                      <SelectItem value="top-right">右上</SelectItem>
                      <SelectItem value="center">居中</SelectItem>
                      <SelectItem value="bottom-left">左下</SelectItem>
                      <SelectItem value="bottom-center">底部居中</SelectItem>
                      <SelectItem value="bottom-right">右下</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="font-size">字体大小</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Input
                      id="font-size"
                      type="number"
                      min="24"
                      max="200"
                      step="4"
                      value={textOverlay.fontSize}
                      onChange={(e) => setTextOverlay(prev => ({ ...prev, fontSize: parseInt(e.target.value) || 96 }))}
                      className="flex-1"
                    />
                    <span className="text-sm text-muted-foreground">px</span>
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setTextOverlay(prev => ({ ...prev, fontSize: 72 }))}
                    >
                      小
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setTextOverlay(prev => ({ ...prev, fontSize: 96 }))}
                    >
                      中
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setTextOverlay(prev => ({ ...prev, fontSize: 120 }))}
                    >
                      大
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setTextOverlay(prev => ({ ...prev, fontSize: 144 }))}
                    >
                      特大
                    </Button>
                  </div>
                </div>
                
                <div>
                  <Label>字体样式</Label>
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      variant={textOverlay.fontWeight === "normal" ? "default" : "outline"}
                      onClick={() => setTextOverlay(prev => ({ ...prev, fontWeight: "normal" }))}
                    >
                      常规
                    </Button>
                    <Button
                      size="sm"
                      variant={textOverlay.fontWeight === "bold" ? "default" : "outline"}
                      onClick={() => setTextOverlay(prev => ({ ...prev, fontWeight: "bold" }))}
                    >
                      加粗
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="text-color">文字颜色</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="text-color"
                        type="color"
                        value={textOverlay.color}
                        onChange={(e) => setTextOverlay(prev => ({ ...prev, color: e.target.value }))}
                        className="h-10 w-20"
                      />
                      <div className="flex gap-1">
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-10 w-10"
                          onClick={() => setTextOverlay(prev => ({ ...prev, color: "#ffffff" }))}
                        >
                          <div className="w-4 h-4 bg-white border rounded" />
                        </Button>
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-10 w-10"
                          onClick={() => setTextOverlay(prev => ({ ...prev, color: "#000000" }))}
                        >
                          <div className="w-4 h-4 bg-black rounded" />
                        </Button>
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-10 w-10"
                          onClick={() => setTextOverlay(prev => ({ ...prev, color: "#ff0000" }))}
                        >
                          <div className="w-4 h-4 bg-red-500 rounded" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="bg-color">背景颜色</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="bg-color"
                        type="color"
                        value={textOverlay.bgColor.slice(0, 7)}
                        onChange={(e) => setTextOverlay(prev => ({ ...prev, bgColor: e.target.value + "aa" }))}
                        className="h-10 w-20"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setTextOverlay(prev => ({ ...prev, bgColor: "transparent" }))}
                      >
                        透明
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="keywords" className="space-y-4 mt-4">
                <div>
                  <Label>添加关键词标签</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      placeholder="输入关键词"
                      value={currentKeyword}
                      onChange={(e) => setCurrentKeyword(e.target.value)}
                      onKeyPress={(e) => e.key === "Enter" && addKeyword()}
                    />
                    <Button size="icon" onClick={addKeyword}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {keywords.map((keyword, index) => (
                    <div
                      key={index}
                      className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center gap-1"
                    >
                      #{keyword}
                      <button
                        onClick={() => removeKeyword(index)}
                        className="hover:bg-primary/20 rounded-full p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="advanced" className="space-y-4 mt-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="batch-mode">批量生成模式</Label>
                  <Switch
                    id="batch-mode"
                    checked={batchMode}
                    onCheckedChange={setBatchMode}
                  />
                </div>
                
                {batchMode && (
                  <div>
                    <Label htmlFor="generation-count">生成数量</Label>
                    <div className="flex items-center gap-4 mt-2">
                      <Slider
                        id="generation-count"
                        min={1}
                        max={30}
                        step={1}
                        value={[generationCount]}
                        onValueChange={(value) => setGenerationCount(value[0])}
                        className="flex-1"
                      />
                      <Input
                        type="number"
                        min={1}
                        max={30}
                        value={generationCount}
                        onChange={(e) => setGenerationCount(parseInt(e.target.value) || 10)}
                        className="w-20"
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      将生成 {generationCount} 个不同的笔记
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-variation">自动变化</Label>
                  <Switch
                    id="auto-variation"
                    checked={autoVariation}
                    onCheckedChange={setAutoVariation}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-title">自动生成标题</Label>
                  <Switch
                    id="auto-title"
                    checked={autoGenerateTitle}
                    onCheckedChange={setAutoGenerateTitle}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="watermark">添加水印</Label>
                  <Switch
                    id="watermark"
                    checked={includeWatermark}
                    onCheckedChange={setIncludeWatermark}
                  />
                </div>
                
                <div>
                  <Label htmlFor="quality">图片质量</Label>
                  <Select value={imageQuality} onValueChange={setImageQuality}>
                    <SelectTrigger id="quality" className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">低质量（快速）</SelectItem>
                      <SelectItem value="medium">中等质量</SelectItem>
                      <SelectItem value="high">高质量</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="template">模板风格</Label>
                  <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                    <SelectTrigger id="template" className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">默认风格</SelectItem>
                      <SelectItem value="minimal">简约风格</SelectItem>
                      <SelectItem value="magazine">杂志风格</SelectItem>
                      <SelectItem value="artistic">艺术风格</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>
            </Tabs>
          </Card>

          <Button
            className="w-full"
            size="lg"
            onClick={batchMode ? handleBatchGenerate : handleGenerateGallery}
            disabled={uploadedImages.length === 0 || isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                {batchMode ? (
                  <>
                    <Grid3X3 className="mr-2 h-4 w-4" />
                    批量生成 {generationCount} 个笔记
                  </>
                ) : (
                  <>
                    <Wand2 className="mr-2 h-4 w-4" />
                    生成图集
                  </>
                )}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 批量生成结果 - 左右分栏布局 */}
      {batchMode && generatedNotes.length > 0 && (
        <div className="fixed inset-0 bg-background z-50 flex">
          {/* 左侧笔记列表 */}
          <div className="w-96 border-r bg-muted/30 flex flex-col">
            <div className="p-4 border-b bg-background">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">生成的笔记 ({generatedNotes.length})</h2>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setGeneratedNotes([])
                    setBatchMode(false)
                    setSelectedNoteId(null)
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-3">
                {generatedNotes.map((note, index) => (
                  <Card
                    key={note.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md",
                      selectedNoteId === note.id && "ring-2 ring-primary"
                    )}
                    onClick={() => setSelectedNoteId(note.id)}
                  >
                    <div className="flex gap-3 p-3">
                      <div className="relative w-24 h-32 flex-shrink-0">
                        <img
                          src={note.coverImage}
                          alt={note.title}
                          className="w-full h-full object-cover rounded"
                        />
                        <Badge className="absolute top-1 left-1 text-xs" variant="secondary">
                          #{index + 1}
                        </Badge>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm line-clamp-2 mb-1">
                          {note.title}
                        </h3>
                        <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                          {note.content.substring(0, 50)}...
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {note.keywords.slice(0, 3).map((keyword, idx) => (
                            <span key={idx} className="text-xs text-primary">
                              #{keyword}
                            </span>
                          ))}
                        </div>
                        <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                          <span>{note.images.length} 张图片</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
            
            <div className="p-4 border-t bg-background">
              <Button
                className="w-full"
                variant="outline"
                onClick={() => {
                  generatedNotes.forEach((note, index) => {
                    const link = document.createElement('a')
                    link.href = note.coverImage
                    link.download = `note_${index + 1}_cover.jpg`
                    link.click()
                  })
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                批量下载所有封面
              </Button>
            </div>
          </div>

          {/* 右侧笔记详情编辑器 */}
          <div className="flex-1 flex flex-col bg-background">
            {selectedNote ? (
              <>
                {/* 顶部工具栏 */}
                <div className="border-b p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <h3 className="text-lg font-semibold">编辑笔记</h3>
                      <Badge variant="outline">#{generatedNotes.findIndex(n => n.id === selectedNote.id) + 1}</Badge>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const content = `${selectedNote.title}\n\n${selectedNote.content}`
                          navigator.clipboard.writeText(content)
                          toast({
                            title: "复制成功",
                            description: "笔记内容已复制到剪贴板"
                          })
                        }}
                      >
                        <Copy className="mr-2 h-4 w-4" />
                        复制文案
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const link = document.createElement('a')
                          link.href = selectedNote.coverImage
                          link.download = `note_cover.jpg`
                          link.click()
                        }}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        下载封面
                      </Button>
                      <Button
                        size="sm"
                        onClick={async () => {
                          // 保存笔记的逻辑
                          toast({
                            title: "保存成功",
                            description: "笔记已保存"
                          })
                        }}
                      >
                        <Save className="mr-2 h-4 w-4" />
                        保存
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 内容编辑区 */}
                <div className="flex-1 overflow-y-auto">
                  <div className="max-w-4xl mx-auto p-6 space-y-6">
                    {/* 封面图预览 */}
                    <div>
                      <Label className="text-base font-medium mb-3 block">封面图</Label>
                      <div className="relative w-full max-w-md">
                        <img
                          src={selectedNote.coverImage}
                          alt="封面图"
                          className="w-full rounded-lg shadow-sm"
                        />
                      </div>
                    </div>

                    {/* 标题编辑 */}
                    <div>
                      <Label htmlFor="note-title" className="text-base font-medium">标题</Label>
                      <Input
                        id="note-title"
                        value={selectedNote.title}
                        onChange={(e) => updateSelectedNote({ title: e.target.value })}
                        className="mt-2 text-lg"
                        placeholder="输入笔记标题"
                      />
                    </div>

                    {/* 内容编辑 */}
                    <div>
                      <Label htmlFor="note-content" className="text-base font-medium">正文内容</Label>
                      <Textarea
                        id="note-content"
                        value={selectedNote.content}
                        onChange={(e) => updateSelectedNote({ content: e.target.value })}
                        className="mt-2 min-h-[300px] resize-none"
                        placeholder="输入笔记正文..."
                      />
                      <p className="text-sm text-muted-foreground mt-2">
                        {selectedNote.content.length} 字
                      </p>
                    </div>

                    {/* 关键词编辑 */}
                    <div>
                      <Label className="text-base font-medium">关键词标签</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {selectedNote.keywords.map((keyword, index) => (
                          <div
                            key={index}
                            className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center gap-1"
                          >
                            #{keyword}
                            <button
                              onClick={() => removeKeywordFromNote(index)}
                              className="hover:bg-primary/20 rounded-full p-0.5"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const newKeyword = prompt("输入新的关键词")
                            if (newKeyword) {
                              addKeywordToNote(newKeyword)
                            }
                          }}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          添加
                        </Button>
                      </div>
                    </div>

                    {/* 图片集 */}
                    {selectedNote.images.length > 1 && (
                      <div>
                        <Label className="text-base font-medium mb-3 block">图片集 ({selectedNote.images.length})</Label>
                        <div className="grid grid-cols-3 gap-3">
                          {selectedNote.images.map((img, index) => (
                            <div key={index} className="relative aspect-square">
                              <img
                                src={img}
                                alt={`图片 ${index + 1}`}
                                className="w-full h-full object-cover rounded"
                              />
                              {index === 0 && (
                                <Badge className="absolute top-2 left-2" variant="secondary">
                                  封面
                                </Badge>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>点击左侧笔记进行查看和编辑</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}