import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database/mysql-client"
import { authenticateApiRequest } from "@/lib/auth/better-auth-utils"

export async function GET(req: NextRequest) {
  // Authenticate the request
  const authResult = await authenticateApiRequest(req)
  if (!authResult.success) {
    return authResult.error
  }

  const user = authResult.user
  try {
    const { searchParams } = new URL(req.url)
    const keyword = searchParams.get('keyword')

    if (!keyword) {
      return NextResponse.json(
        { error: "Keyword is required" },
        { status: 400 }
      )
    }

    // 获取缓存状态
    const cache = await prisma.sentimentAnalysisCache.findUnique({
      where: {
        userId_keyword: {
          userId: user.id,
          keyword
        }
      }
    })

    if (!cache) {
      return NextResponse.json({
        exists: false
      })
    }

    // 获取详细的笔记情绪数据（如果需要）
    let details = null
    if (cache.status === 'completed' && searchParams.get('includeDetails') === 'true') {
      details = await prisma.sentimentAnalysisDetail.findMany({
        where: { cacheId: cache.id },
        orderBy: { createdAt: 'desc' }
      })
    }

    return NextResponse.json({
      exists: true,
      cache,
      details
    })

  } catch (error) {
    console.error("Error getting analysis status:", error)
    return NextResponse.json(
      { error: "Failed to get analysis status" },
      { status: 500 }
    )
  }
}