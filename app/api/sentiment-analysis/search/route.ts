import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database/mysql-client"
import { authenticateApiRequest } from "@/lib/auth/better-auth-utils"
import { searchNotesSchema } from "@/lib/validations/sentiment-analysis"
import { Prisma } from "@prisma/client"

export async function POST(req: NextRequest) {
  // Authenticate the request
  const authResult = await authenticateApiRequest(req)
  if (!authResult.success) {
    return authResult.error
  }

  const user = authResult.user
  try {
    const body = await req.json()
    const validation = searchNotesSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid request data", issues: validation.error.issues },
        { status: 400 }
      )
    }

    const { keyword, limit = 100, offset = 0 } = validation.data

    // 构建搜索条件
    const whereCondition: Prisma.NoteWhereInput = {
      userId: user.id,
      OR: [
        { title: { contains: keyword } },
        { content: { contains: keyword } }
      ]
    }

    // 获取符合条件的笔记总数
    const totalCount = await prisma.note.count({ where: whereCondition })

    // 获取笔记列表
    const notes = await prisma.note.findMany({
      where: whereCondition,
      orderBy: { publishedAt: 'desc' },
      take: limit,
      skip: offset,
      select: {
        id: true,
        noteId: true,
        title: true,
        content: true,
        coverImage: true,
        likes: true,
        commentsCount: true,
        saves: true,
        publishedAt: true,
        createdAt: true
      }
    })

    // 计算统计数据
    const stats = {
      totalNotes: totalCount,
      totalLikes: notes.reduce((sum, note) => sum + note.likes, 0),
      totalComments: notes.reduce((sum, note) => sum + note.commentsCount, 0),
      totalSaves: notes.reduce((sum, note) => sum + note.saves, 0)
    }

    return NextResponse.json({
      notes,
      stats,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    })

  } catch (error) {
    console.error("Error searching notes:", error)
    return NextResponse.json(
      { error: "Failed to search notes" },
      { status: 500 }
    )
  }
}