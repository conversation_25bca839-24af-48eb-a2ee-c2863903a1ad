import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database/mysql-client"
import { authenticateApiRequest } from "@/lib/auth/better-auth-utils"

export async function GET(req: NextRequest) {
  // Authenticate the request
  const authResult = await authenticateApiRequest(req)
  if (!authResult.success) {
    return authResult.error
  }

  const user = authResult.user

  try {
    const { searchParams } = new URL(req.url)
    const cacheId = searchParams.get('cacheId')
    const sentiment = searchParams.get('sentiment')

    if (!cacheId || !sentiment) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      )
    }

    // 验证缓存是否属于当前用户
    const cache = await prisma.sentimentAnalysisCache.findFirst({
      where: {
        id: cacheId,
        userId: user.id
      }
    })

    if (!cache) {
      return NextResponse.json(
        { error: "Cache not found or unauthorized" },
        { status: 404 }
      )
    }

    // 获取该情绪类别的笔记详情
    const sentimentDetails = await prisma.sentimentAnalysisDetail.findMany({
      where: {
        cacheId,
        sentiment
      }
    })

    const noteIds = sentimentDetails.map(d => d.noteId)

    // 获取笔记详情
    const notes = await prisma.note.findMany({
      where: {
        noteId: { in: noteIds },
        userId: user.id
      },
      select: {
        id: true,
        noteId: true,
        title: true,
        content: true,
        coverImage: true,
        likes: true,
        commentsCount: true,
        saves: true,
        publishedAt: true,
        url: true
      },
      orderBy: { likes: 'desc' }
    })

    // 将sentiment details的信息合并到notes中
    const notesWithSentiment = notes.map(note => {
      const detail = sentimentDetails.find(d => d.noteId === note.noteId)
      return {
        ...note,
        confidence: detail?.confidence || 0,
        keywords: detail?.keywords || []
      }
    })

    return NextResponse.json({
      sentiment,
      totalCount: notesWithSentiment.length,
      notes: notesWithSentiment
    })

  } catch (error) {
    console.error("Error fetching sentiment notes:", error)
    return NextResponse.json(
      { error: "Failed to fetch notes" },
      { status: 500 }
    )
  }
}