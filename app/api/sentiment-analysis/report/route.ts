import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database/mysql-client"
import { authenticateApiRequest } from "@/lib/auth/better-auth-utils"
import { generateReportSchema } from "@/lib/validations/sentiment-analysis"
import { ContentGenerationService } from "@/lib/ai/content-generation-service"
import { pointsIntegration } from "@/lib/points/points-integration-service"

export async function POST(req: NextRequest) {
  // Authenticate the request
  const authResult = await authenticateApiRequest(req)
  if (!authResult.success) {
    return authResult.error
  }

  const user = authResult.user
  try {
    const body = await req.json()
    const validation = generateReportSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid request data", issues: validation.error.issues },
        { status: 400 }
      )
    }

    const { cacheId, forceRefresh } = validation.data

    // 获取缓存数据
    const cache = await prisma.sentimentAnalysisCache.findUnique({
      where: { 
        id: cacheId,
        userId: user.id
      }
    })

    if (!cache) {
      return NextResponse.json(
        { error: "Analysis cache not found" },
        { status: 404 }
      )
    }

    if (cache.status !== 'completed') {
      return NextResponse.json(
        { error: "Analysis not completed yet" },
        { status: 400 }
      )
    }

    // 如果已有报告且不超过12小时，且没有强制刷新，直接返回
    if (cache.aiSummaryReport && !forceRefresh) {
      const reportAge = Date.now() - cache.updatedAt.getTime()
      const maxReportAge = 12 * 60 * 60 * 1000 // 12小时
      
      if (reportAge < maxReportAge) {
        return NextResponse.json({
          report: cache.aiSummaryReport,
          cached: true
        })
      }
    }

    // 检查积分余额（重新生成报告需要扣除积分）
    const balanceCheck = await pointsIntegration.checkBalance(
      user.id,
      'ai_report'
    )

    if (!balanceCheck.sufficient) {
      return NextResponse.json(
        { 
          error: `积分不足，需要${balanceCheck.requiredPoints}积分，当前余额${balanceCheck.currentBalance}积分`,
          requiredPoints: balanceCheck.requiredPoints,
          currentBalance: balanceCheck.currentBalance
        },
        { status: 402 }
      )
    }

    // 获取用户的AI设置
    const aiSetting = await prisma.aiSetting.findUnique({
      where: { userId: user.id }
    })

    if (!aiSetting) {
      return NextResponse.json(
        { error: "User AI settings not found" },
        { status: 400 }
      )
    }

    // 扣除积分
    try {
      await pointsIntegration.consumePoints(
        user.id,
        'ai_report',
        {
          relatedId: cacheId,
          description: `AI分析报告生成 - ${cache.keyword}`,
          metadata: {
            cacheId,
            keyword: cache.keyword
          }
        }
      )
    } catch (pointsError) {
      console.error("Failed to consume points:", pointsError)
      return NextResponse.json(
        { error: "积分扣除失败，请稍后重试" },
        { status: 500 }
      )
    }

    // 生成报告
    const report = await generateSummaryReport(cache, aiSetting)

    // 更新缓存
    await prisma.sentimentAnalysisCache.update({
      where: { id: cacheId },
      data: {
        aiSummaryReport: report
      }
    })

    return NextResponse.json({
      report,
      cached: false
    })

  } catch (error) {
    console.error("Error generating report:", error)
    return NextResponse.json(
      { error: "Failed to generate report" },
      { status: 500 }
    )
  }
}

async function generateSummaryReport(cache: any, aiSetting: any): Promise<string> {
  const sentimentDist = cache.sentimentDistribution as any
  const topNotes = cache.topNotesData as any[]
  const keywords = cache.keywordsCloud as any[]

  // 构建热门笔记内容部分
  let topNotesContent = '暂无笔记数据'
  
  if (topNotes && topNotes.length > 0) {
    // 先构建所有笔记内容
    const allNotesContent = topNotes.slice(0, 10).map((note, index) => {
      return `
【笔记${index + 1}】
标题：${note.title || '无标题'}
内容：${note.content || '无内容'}
点赞数：${note.likes || 0}
评论数：${note.comments || 0}
收藏数：${note.saves || 0}
情绪：${note.sentiment || '未知'}`
    })
    
    // 计算总字数
    const totalContent = allNotesContent.join('\n')
    const totalLength = totalContent.length
    
    // 如果超过2万字，只取前1万字
    if (totalLength > 20000) {
      let currentLength = 0
      const truncatedNotes = []
      
      for (const noteContent of allNotesContent) {
        if (currentLength + noteContent.length <= 10000) {
          truncatedNotes.push(noteContent)
          currentLength += noteContent.length
        } else {
          // 截取部分内容以达到1万字限制
          const remainingLength = 10000 - currentLength
          if (remainingLength > 100) { // 至少保留100字才有意义
            const truncated = noteContent.substring(0, remainingLength) + '\n...(内容已截断)'
            truncatedNotes.push(truncated)
          }
          break
        }
      }
      
      topNotesContent = truncatedNotes.join('\n') + '\n\n注：由于内容过长，仅展示前1万字的内容。'
    } else {
      topNotesContent = totalContent
    }
  }

  const prompt = `基于以下舆情分析数据，生成一份专业的分析报告：

关键词：${cache.keyword}
分析时间：${cache.updatedAt.toLocaleDateString()}

数据概览：
- 分析笔记总数：${cache.totalNotes}
- 总点赞数：${cache.totalLikes}
- 总评论数：${cache.totalComments}
- 总收藏数：${cache.totalCollects}

笔记情绪分布：
- 正面：${sentimentDist?.notes?.positive || 0}篇
- 负面：${sentimentDist?.notes?.negative || 0}篇
- 中性：${sentimentDist?.notes?.neutral || 0}篇

评论情绪分布：
- 正面：${sentimentDist?.comments?.positive || 0}条
- 负面：${sentimentDist?.comments?.negative || 0}条
- 中性：${sentimentDist?.comments?.neutral || 0}条

热门关键词：
${keywords?.slice(0, 10).map(k => `${k.text}(${k.value}次)`).join('、') || '暂无数据'}

热门笔记内容（前10篇）：
${topNotesContent}

请基于以上数据，特别是笔记的具体内容，生成一份包含以下部分的深度分析报告：
1. 舆情总体概况（结合具体笔记内容分析）
2. 情绪趋势分析（分析正面、负面、中性的比例和具体表现）
3. 关键发现（从笔记内容中提炼具体洞察）
4. 用户关注热点（基于关键词和笔记内容分析）
5. 营销建议（3-5条具体建议，基于用户实际反馈）
6. 综合分析

请使用专业但易懂的语言，报告要有洞察力和实用性，内容务必要详细丰富有内容，并充分利用笔记内容提供具体的例证。`

  try {
    // 创建内容生成服务实例
    const contentService = new ContentGenerationService(
      aiSetting.encryptedApiKey || undefined,
      undefined // 使用默认的base URL
    )
    console.log(prompt)
    
    // 使用ContentGenerationService的OpenAI客户端进行文本生成
    const openaiClient = (contentService as any).openaiClient
    const completion = await openaiClient.chat.completions.create({
      model: aiSetting.aiModel || 'gpt-4',
      messages: [
        { 
          role: 'system', 
          content: '你是一位专业的社交媒体舆情分析专家，擅长从数据中提取有价值的洞察并给出实用的营销建议。' 
        },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 6500
    })
    
    const response = completion.choices[0]?.message?.content || ''

    return response
  } catch (error) {
    console.error("Error generating AI report:", error)
    throw error
  }
}