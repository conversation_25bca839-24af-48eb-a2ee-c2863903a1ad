import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database/mysql-client"
import { authenticateApiRequest } from "@/lib/auth/better-auth-utils"
import { analyzeRequestSchema } from "@/lib/validations/sentiment-analysis"
import { ContentGenerationService } from "@/lib/ai/content-generation-service"
import { subDays } from "date-fns"
import { ModeToggle } from "@/components/mode-toggle"
import { pointsIntegration } from "@/lib/points/points-integration-service"

interface SentimentResult {
  sentiment: 'positive' | 'negative' | 'neutral'
  confidence: number
  keywords: string[]
}

export async function POST(req: NextRequest) {
  // Authenticate the request
  const authResult = await authenticateApiRequest(req)
  if (!authResult.success) {
    return authResult.error
  }

  const user = authResult.user
  try {
    const body = await req.json()
    const validation = analyzeRequestSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid request data", issues: validation.error.issues },
        { status: 400 }
      )
    }

    const { keyword, forceRefresh } = validation.data

    // 检查缓存
    if (!forceRefresh) {
      const existingCache = await prisma.sentimentAnalysisCache.findUnique({
        where: {
          userId_keyword: {
            userId: user.id,
            keyword
          }
        }
      })

      if (existingCache && existingCache.status === 'completed') {
        const cacheAge = Date.now() - existingCache.updatedAt.getTime()
        const maxAge = 24 * 60 * 60 * 1000 // 24小时

        if (cacheAge < maxAge) {
          return NextResponse.json({
            cached: true,
            data: existingCache
          })
        }
      }
    }

    // 检查积分余额（新分析或强制刷新都需要扣除积分）
    const balanceCheck = await pointsIntegration.checkBalance(
      user.id,
      'sentiment_analysis'
    )

    if (!balanceCheck.sufficient) {
      return NextResponse.json(
        { 
          error: `积分不足，需要${balanceCheck.requiredPoints}积分，当前余额${balanceCheck.currentBalance}积分`,
          requiredPoints: balanceCheck.requiredPoints,
          currentBalance: balanceCheck.currentBalance
        },
        { status: 402 }
      )
    }

    // 创建或更新缓存记录
    const cache = await prisma.sentimentAnalysisCache.upsert({
      where: {
        userId_keyword: {
          userId: user.id,
          keyword
        }
      },
      create: {
        userId: user.id,
        keyword,
        status: 'processing'
      },
      update: {
        status: 'processing',
        updatedAt: new Date()
      }
    })

    // 扣除积分
    try {
      await pointsIntegration.consumePoints(
        user.id,
        'sentiment_analysis',
        {
          relatedId: cache.id,
          description: `舆情分析：${keyword}`,
          metadata: {
            keyword,
            cacheId: cache.id
          }
        }
      )
    } catch (pointsError) {
      // 如果扣除积分失败，更新缓存状态为失败
      await prisma.sentimentAnalysisCache.update({
        where: { id: cache.id },
        data: { status: 'failed' }
      })
      console.error("Failed to consume points:", pointsError)
      return NextResponse.json(
        { error: "积分扣除失败，请稍后重试" },
        { status: 500 }
      )
    }

    // 异步处理分析
    processAnalysis(cache.id, user.id, keyword).catch(console.error)

    return NextResponse.json({
      cached: false,
      data: cache,
      message: "分析已开始，请稍后查看结果"
    })

  } catch (error) {
    console.error("Error analyzing sentiment:", error)
    return NextResponse.json(
      { error: "Failed to analyze sentiment" },
      { status: 500 }
    )
  }
}

async function processAnalysis(cacheId: string, userId: string, keyword: string) {
  try {
    // 获取用户的AI设置
    const aiSetting = await prisma.aiSetting.findUnique({
      where: { userId }
    })

    if (!aiSetting) {
      throw new Error("User AI settings not found")
    }

    // 创建内容生成服务实例
    const contentService = new ContentGenerationService(
      aiSetting.encryptedApiKey,
      undefined // 使用默认的base URL
    )

    // 搜索相关笔记
    const notes = await prisma.note.findMany({
      where: {
        userId,
        OR: [
          { title: { contains: keyword } },
          { content: { contains: keyword } }
        ]
      },
      orderBy: { publishedAt: 'desc' }
    })

    const noteIds = notes.map(n => n.noteId)

    // 获取评论数据
    const comments = await prisma.comment.findMany({
      where: {
        noteId: { in: noteIds }
      }
    })

    // 分析笔记情绪
    const noteAnalysisResults: SentimentResult[] = []
    const batchSize = 10

    for (let i = 0; i < notes.length; i += batchSize) {
      const batch = notes.slice(i, i + batchSize)
      const promises = batch.map(note => analyzeSentiment(note.content || '', aiSetting, contentService))
      const results = await Promise.all(promises)
      noteAnalysisResults.push(...results)
    }

    // 分析评论情绪
    const commentAnalysisResults: SentimentResult[] = []
    
    for (let i = 0; i < comments.length; i += batchSize) {
      const batch = comments.slice(i, i + batchSize)
      const promises = batch.map(comment => analyzeSentiment(comment.content, aiSetting, contentService))
      const results = await Promise.all(promises)
      commentAnalysisResults.push(...results)
    }

    // 统计数据
    const stats = {
      totalNotes: notes.length,
      totalLikes: notes.reduce((sum, note) => sum + note.likes, 0),
      totalComments: notes.reduce((sum, note) => sum + note.commentsCount, 0),
      totalCollects: notes.reduce((sum, note) => sum + note.saves, 0),
      positiveNotes: noteAnalysisResults.filter(r => r.sentiment === 'positive').length,
      negativeNotes: noteAnalysisResults.filter(r => r.sentiment === 'negative').length,
      neutralNotes: noteAnalysisResults.filter(r => r.sentiment === 'neutral').length,
      positiveComments: commentAnalysisResults.filter(r => r.sentiment === 'positive').length,
      negativeComments: commentAnalysisResults.filter(r => r.sentiment === 'negative').length,
      neutralComments: commentAnalysisResults.filter(r => r.sentiment === 'neutral').length
    }

    // 生成词云数据
    const allKeywords = [...noteAnalysisResults, ...commentAnalysisResults]
      .flatMap(r => r.keywords)
    const keywordFreq = allKeywords.reduce((acc, keyword) => {
      acc[keyword] = (acc[keyword] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const keywordsCloud = Object.entries(keywordFreq)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 50)
      .map(([text, value]) => ({ text, value }))

    // 生成前10个热门笔记数据
    const topNotesData = notes
      .sort((a, b) => b.likes - a.likes)
      .slice(0, 10)
      .map((note) => {
        // 找到对应的情绪分析结果
        const noteIndex = notes.findIndex(n => n.id === note.id)
        const sentiment = noteAnalysisResults[noteIndex]?.sentiment || 'unknown'
        
        return {
          id: note.id,
          noteId: note.noteId,
          title: note.title,
          content: note.content,
          likes: note.likes,
          comments: note.commentsCount,
          saves: note.saves,
          sentiment: sentiment
        }
      })

    // 生成趋势数据
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = subDays(new Date(), 6 - i)
      return {
        date: date.toISOString().split('T')[0],
        positive: 0,
        negative: 0,
        neutral: 0
      }
    })

    notes.forEach((note, index) => {
      if (note.publishedAt) {
        const dateStr = note.publishedAt.toISOString().split('T')[0]
        const dayData = last7Days.find(d => d.date === dateStr)
        if (dayData) {
          const sentiment = noteAnalysisResults[index]?.sentiment || 'neutral'
          dayData[sentiment]++
        }
      }
    })

    // 更新缓存
    await prisma.sentimentAnalysisCache.update({
      where: { id: cacheId },
      data: {
        ...stats,
        topNotesData,
        keywordsCloud,
        sentimentDistribution: {
          notes: {
            positive: stats.positiveNotes,
            negative: stats.negativeNotes,
            neutral: stats.neutralNotes
          },
          comments: {
            positive: stats.positiveComments,
            negative: stats.negativeComments,
            neutral: stats.neutralComments
          }
        },
        trendData: last7Days,
        noteIds,
        status: 'completed'
      }
    })

    // 保存详细分析结果
    const detailsToSave = notes.map((note, index) => ({
      cacheId,
      noteId: note.noteId,
      sentiment: noteAnalysisResults[index]?.sentiment || 'neutral',
      confidence: noteAnalysisResults[index]?.confidence || 0,
      keywords: noteAnalysisResults[index]?.keywords || []
    }))

    await prisma.sentimentAnalysisDetail.createMany({
      data: detailsToSave,
      skipDuplicates: true
    })

  } catch (error) {
    console.error("Error processing analysis:", error)
    await prisma.sentimentAnalysisCache.update({
      where: { id: cacheId },
      data: {
        status: 'failed',
        metadata: { error: error.message }
      }
    })
  }
}

async function analyzeSentiment(text: string, aiSetting: any, contentService: ContentGenerationService): Promise<SentimentResult> {
  if (!text || text.trim().length === 0) {
    return {
      sentiment: 'neutral',
      confidence: 1.0,
      keywords: []
    }
  }

  try {
    const prompt = `请分析以下文本的情绪倾向，并返回JSON格式的结果。

文本内容：
${text.substring(0, 500)}

请返回以下格式的JSON（不要包含其他文字）：
{
  "sentiment": "positive/negative/neutral",
  "confidence": 0.8,
  "keywords": ["关键词1", "关键词2", "关键词3"]
}`

    // 使用ContentGenerationService的OpenAI客户端进行文本生成
    const openaiClient = (contentService as any).openaiClient
    const completion = await openaiClient.chat.completions.create({
      // model: aiSetting.aiModel || 'google/gemini-2.5-flash-lite-preview-06-17',
      model:"google/gemini-2.5-flash-lite-preview-06-17",
      messages: [
        { 
          role: 'system', 
          content: '你是一个专业的文本情绪分析专家。如果笔记内容提到了差评或者不满等都要标记负面，如果用户给与好评积极评价则为正面，如果用户客观陈述和描述现象则为中兴。请按照要求对内容进行情绪分析' 
        },
        { role: 'user', content: prompt }
      ],
      temperature: 0.3,
      max_tokens: 200,
      response_format: { type: 'json_object' }
    })
    
    const response = completion.choices[0]?.message?.content || '{}'
    console.log("情绪分析结果为：", response)
    const result = JSON.parse(response)
    return {
      sentiment: result.sentiment || 'neutral',
      confidence: result.confidence || 0.5,
      keywords: result.keywords || []
    }
  } catch (error) {
    console.error("Error in sentiment analysis:", error)
    return {
      sentiment: 'neutral',
      confidence: 0.5,
      keywords: []
    }
  }
}