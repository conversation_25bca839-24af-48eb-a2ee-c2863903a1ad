import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { batchGeneratedNotesRepository } from '@/lib/database/batch-generated-notes-repository'
import { getCurrentUserFromRequest } from '@/lib/auth/better-auth-utils'

// Schema for updating a note
const UpdateNoteSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  content: z.string().optional(),
  coverImage: z.string().optional(),
  contentImages: z.array(z.any()).optional(),
  isAddedToPublish: z.boolean().optional(),
  publishedNoteId: z.string().optional()
})

// GET /api/image-batch-generator/[id] - Get single batch generated note
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    const note = await batchGeneratedNotesRepository.getBatchGeneratedNote(params.id, userId)
    
    if (!note) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 })
    }

    return NextResponse.json(note)
  } catch (error) {
    console.error('Error fetching batch generated note:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch batch generated note' 
    }, { status: 500 })
  }
}

// PATCH /api/image-batch-generator/[id] - Update batch generated note
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    const body = await request.json()
    const validation = UpdateNoteSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validation.error.errors 
      }, { status: 400 })
    }

    // Check if note exists and belongs to user
    const existingNote = await batchGeneratedNotesRepository.getBatchGeneratedNote(params.id, userId)
    if (!existingNote) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 })
    }

    const updatedNote = await batchGeneratedNotesRepository.updateBatchGeneratedNote(
      params.id,
      userId,
      validation.data
    )

    return NextResponse.json(updatedNote)
  } catch (error) {
    console.error('Error updating batch generated note:', error)
    return NextResponse.json({ 
      error: 'Failed to update batch generated note' 
    }, { status: 500 })
  }
}

// DELETE /api/image-batch-generator/[id] - Delete batch generated note
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    // Check if note exists and belongs to user
    const existingNote = await batchGeneratedNotesRepository.getBatchGeneratedNote(params.id, userId)
    if (!existingNote) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 })
    }

    await batchGeneratedNotesRepository.deleteBatchGeneratedNote(params.id, userId)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting batch generated note:', error)
    return NextResponse.json({ 
      error: 'Failed to delete batch generated note' 
    }, { status: 500 })
  }
}