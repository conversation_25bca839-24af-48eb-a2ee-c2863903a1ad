import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { batchGeneratedNotesRepository } from '@/lib/database/batch-generated-notes-repository'
import { getCurrentUserFromRequest } from '@/lib/auth/better-auth-utils'

// Schema for updating publish status
const UpdatePublishStatusSchema = z.object({
  isAddedToPublish: z.boolean(),
  publishedNoteId: z.string().optional()
})

// PATCH /api/image-batch-generator/[id]/publish-status - Update publish status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    const body = await request.json()
    const validation = UpdatePublishStatusSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validation.error.errors 
      }, { status: 400 })
    }

    // Check if note exists and belongs to user
    const existingNote = await batchGeneratedNotesRepository.getBatchGeneratedNote(params.id, userId)
    if (!existingNote) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 })
    }

    const { isAddedToPublish, publishedNoteId } = validation.data
    const updatedNote = await batchGeneratedNotesRepository.updatePublishStatus(
      params.id,
      userId,
      isAddedToPublish,
      publishedNoteId
    )

    return NextResponse.json({
      success: true,
      note: updatedNote
    })
  } catch (error) {
    console.error('Error updating publish status:', error)
    return NextResponse.json({ 
      error: 'Failed to update publish status' 
    }, { status: 500 })
  }
}