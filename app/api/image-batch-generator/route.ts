import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { batchGeneratedNotesRepository } from '@/lib/database/batch-generated-notes-repository'
import { getCurrentUserFromRequest } from '@/lib/auth/better-auth-utils'

// Schema for creating batch generated notes
const CreateBatchSchema = z.object({
  sessionName: z.string().min(1).max(255),
  notes: z.array(z.object({
    title: z.string().min(1).max(255),
    content: z.string(),
    coverImage: z.string(), // Base64 or URL
    contentImages: z.array(z.any()),
    keywords: z.array(z.string()),
    templateSettings: z.any(),
    textOverlayConfig: z.any(),
    generationConfig: z.any()
  }))
})

// Schema for query parameters
const QuerySchema = z.object({
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(50).optional().default(10),
  batchSessionId: z.string().optional(),
  isAddedToPublish: z.coerce.boolean().optional()
})

// POST /api/image-batch-generator - Save batch generated notes
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    const body = await request.json()
    const validation = CreateBatchSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validation.error.errors 
      }, { status: 400 })
    }

    const { sessionName, notes } = validation.data

    // Create batch session
    const session = await batchGeneratedNotesRepository.createBatchSession({
      userId,
      sessionName,
      totalGenerated: notes.length,
      metadata: {
        generatedAt: new Date().toISOString()
      }
    })
    
    console.log('Created session:', session.id, 'with', notes.length, 'notes')

    // Prepare notes for batch creation
    const notesData = notes.map((note, index) => ({
      userId,
      batchSessionId: session.id,
      batchIndex: index,
      ...note
    }))
    
    console.log('Prepared notes data:', notesData.length, 'notes')
    console.log('First note sample:', {
      ...notesData[0],
      coverImage: notesData[0]?.coverImage?.substring(0, 50) + '...',
      contentImages: notesData[0]?.contentImages?.length + ' images'
    })

    // Create all notes in batch
    const result = await batchGeneratedNotesRepository.createBatchGeneratedNotes(notesData)
    
    console.log('Created notes result:', result)

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      notesCreated: result.count
    })
  } catch (error) {
    console.error('Error creating batch generated notes:', error)
    return NextResponse.json({ 
      error: 'Failed to save batch generated notes',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET /api/image-batch-generator - Get batch generated notes
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    const { searchParams } = new URL(request.url)
    const queryValidation = QuerySchema.safeParse({
      page: searchParams.get('page') || undefined,
      limit: searchParams.get('limit') || undefined,
      batchSessionId: searchParams.get('batchSessionId'),
      isAddedToPublish: searchParams.get('isAddedToPublish')
    })

    if (!queryValidation.success) {
      return NextResponse.json({ 
        error: 'Invalid query parameters', 
        details: queryValidation.error.errors 
      }, { status: 400 })
    }

    const filters = {
      ...queryValidation.data,
      userId
    }

    const result = await batchGeneratedNotesRepository.getBatchGeneratedNotes(filters)

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching batch generated notes:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch batch generated notes' 
    }, { status: 500 })
  }
}