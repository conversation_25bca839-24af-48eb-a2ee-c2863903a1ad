import { NextRequest, NextResponse } from 'next/server'
import { batchGeneratedNotesRepository } from '@/lib/database/batch-generated-notes-repository'
import { getCurrentUserFromRequest } from '@/lib/auth/better-auth-utils'

// GET /api/image-batch-generator/stats - Get user statistics
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    const stats = await batchGeneratedNotesRepository.getUserStatistics(userId)

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching statistics:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch statistics' 
    }, { status: 500 })
  }
}