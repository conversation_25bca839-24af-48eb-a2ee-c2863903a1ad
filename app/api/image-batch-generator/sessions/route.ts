import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { batchGeneratedNotesRepository } from '@/lib/database/batch-generated-notes-repository'
import { getCurrentUserFromRequest } from '@/lib/auth/better-auth-utils'

// Schema for query parameters
const QuerySchema = z.object({
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(50).optional().default(10)
})

// GET /api/image-batch-generator/sessions - Get batch generation sessions
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }
    const userId = user.id

    const { searchParams } = new URL(request.url)
    const queryValidation = QuerySchema.safeParse({
      page: searchParams.get('page') || undefined,
      limit: searchParams.get('limit') || undefined
    })

    if (!queryValidation.success) {
      return NextResponse.json({ 
        error: 'Invalid query parameters', 
        details: queryValidation.error.errors 
      }, { status: 400 })
    }

    const { page, limit } = queryValidation.data
    const result = await batchGeneratedNotesRepository.getBatchSessions(userId, page, limit)

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching batch sessions:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch batch sessions' 
    }, { status: 500 })
  }
}