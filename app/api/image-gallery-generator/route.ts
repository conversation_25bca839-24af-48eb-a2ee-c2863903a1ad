import { NextRequest, NextResponse } from "next/server"
import { getCurrentUserFromRequest } from "@/lib/auth/better-auth-utils"
import { getR2Client } from "@/lib/storage/r2-client"
import { v4 as uuidv4 } from "uuid"

export const maxDuration = 60

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse FormData
    const formData = await request.formData()
    const images = formData.getAll('images') as File[]
    const template = formData.get('template') as string
    
    // Safely parse keywords
    let keywords: string[] = []
    try {
      const keywordsStr = formData.get('keywords') as string
      if (keywordsStr) {
        keywords = JSON.parse(keywordsStr)
      }
    } catch (e) {
      console.error("Error parsing keywords:", e)
      keywords = []
    }
    
    const includeWatermark = formData.get('includeWatermark') === 'true'
    const imageQuality = formData.get('imageQuality') as string
    const processedCoverImage = formData.get('processedCoverImage') as File | null

    if (images.length === 0) {
      return NextResponse.json({ error: "No images provided" }, { status: 400 })
    }

    const r2Client = getR2Client()
    const galleryId = uuidv4()
    const galleryImages: string[] = []

    try {
      // Upload processed cover image if provided
      if (processedCoverImage) {
        const coverFileName = `galleries/${user.id}/${galleryId}/cover.jpg`
        const coverUploadResult = await r2Client.uploadFile(coverFileName, processedCoverImage)
        
        if (coverUploadResult.success && coverUploadResult.url) {
          galleryImages.push(coverUploadResult.url)
        }
      }

      // Upload all original images
      for (let i = 0; i < images.length; i++) {
        const image = images[i]
        const fileName = `galleries/${user.id}/${galleryId}/image_${i + 1}.jpg`
        const uploadResult = await r2Client.uploadFile(fileName, image)
        
        if (uploadResult.success && uploadResult.url) {
          galleryImages.push(uploadResult.url)
        }
      }

      return NextResponse.json({
        success: true,
        galleryId,
        galleryImages,
        template,
        keywords,
        message: `成功上传 ${galleryImages.length} 张图片`
      })

    } catch (error) {
      console.error("Error uploading images:", error)
      return NextResponse.json(
        { error: "Failed to upload images" },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error("Error processing request:", error)
    return NextResponse.json(
      { 
        error: "Failed to process request",
        message: error instanceof Error ? error.message : "Unknown error",
        details: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}