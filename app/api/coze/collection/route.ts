import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database/mysql-client"
import { authenticateApiRequest } from "@/lib/auth/better-auth-utils"

// Coze API配置
const COZE_API_KEY = process.env.COZE_API_KEY // 从环境变量中获取API密钥
const COZE_WORKFLOW_ID = process.env.COZE_WORKFLOW_ID // 从环境变量中获取工作流ID

// API处理函数
export async function POST(request: NextRequest) {
  // 将user_id定义在try块外部，以便在catch块中也能访问
  let user_id: string | undefined
  
  try {
    // Authenticate the request first
    const authResult = await authenticateApiRequest(request)
    if (!authResult.success) {
      return authResult.error
    }

    const user = authResult.user
    user_id = user.id // Use authenticated user ID

    // 获取请求体
    const body = await request.json()

    // 记录请求体以便调试
    console.log("请求体:", JSON.stringify(body))

    // 验证必需的字段 (不再需要验证user_id)
    const { xhs_cookie, keywords, count, collection_type } = body

    if (!xhs_cookie) {
      console.log("错误: 缺少小红书Cookie")
      return NextResponse.json({ error: "缺少小红书Cookie" }, { status: 400 })
    }

    if (!keywords) {
      console.log("错误: 缺少关键词")
      return NextResponse.json({ error: "缺少关键词" }, { status: 400 })
    }

    // 确保我们有API密钥和工作流ID
    if (!COZE_API_KEY || !COZE_WORKFLOW_ID) {
      console.error("Coze API配置缺失", { COZE_API_KEY: !!COZE_API_KEY, COZE_WORKFLOW_ID: !!COZE_WORKFLOW_ID })
      return NextResponse.json({ error: "服务器配置错误" }, { status: 500 })
    }

    // 新增：获取用户活跃的API密钥
    console.log("查询用户API密钥, user_id:", user_id)
    let apiKeyValue: string
    try {
      const apiKeyData = await prisma.apiKey.findFirst({
        where: {
          userId: user_id,
          isActive: true
        },
        select: {
          keyValue: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      if (!apiKeyData) {
        console.log("未找到有效的API密钥, user_id:", user_id)
        return NextResponse.json({ error: "未找到有效的API密钥，请先创建API密钥" }, { status: 400 })
      }

      apiKeyValue = apiKeyData.keyValue
    } catch (apiKeyError) {
      console.error("获取API密钥失败:", apiKeyError)
      return NextResponse.json({ error: "获取API密钥失败" }, { status: 500 })
    }

    // 准备要发送到Coze的数据
    const cozeData = {
      workflow_id: COZE_WORKFLOW_ID,
      parameters: {
        api_key: apiKeyValue, // 使用从数据库获取的API密钥
        xhs_cookie: xhs_cookie,
        keywords: keywords,
        count: count || 20, // 默认10条
        sort:  0 // 默认热门
      },
      is_async: true // 启用异步处理
    }

    console.log("发送到Coze的数据:", {
      workflow_id: COZE_WORKFLOW_ID,
      parameterKeys: Object.keys(cozeData.parameters),
      is_async: true
    })

    // 调用Coze API
    const cozeResponse = await fetch('https://api.coze.cn/v1/workflow/run', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${COZE_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(cozeData)
    })
    console.log("Coze响应状态:", cozeResponse.status, cozeResponse.statusText)

    // 检查Coze API的响应
    if (!cozeResponse.ok) {
      const errorData = await cozeResponse.json()
      console.error("Coze API调用失败:", errorData)

      // 记录失败的API调用到日志表
      try {
        await prisma.$queryRaw`
          INSERT INTO log_coze_invoke (id, user_id, invoke_param, resp_info, extend, created_at)
          VALUES (UUID(), ${user_id}, ${JSON.stringify(cozeData)}, ${JSON.stringify(errorData)}, 'API调用失败', NOW())
        `
      } catch (logError) {
        console.error("记录失败日志时出错:", logError)
      }

      return NextResponse.json({ error: "Coze API调用失败", details: errorData }, { status: 500 })
    }

    // 获取Coze的响应数据
    const cozeResult = await cozeResponse.json()
    console.log("Coze采集响应数据:", cozeResult)

    // 更新密钥的最后使用时间
    try {
      await prisma.apiKey.updateMany({
        where: {
          keyValue: apiKeyValue
        },
        data: {
          lastUsedAt: new Date()
        }
      })
    } catch (updateError) {
      console.error("更新API密钥最后使用时间失败:", updateError)
    }

    // 记录成功的API调用到日志表
    try {
      await prisma.$queryRaw`
        INSERT INTO log_coze_invoke (id, user_id, invoke_param, resp_info, extend, created_at)
        VALUES (UUID(), ${user_id}, ${JSON.stringify(cozeData)}, ${JSON.stringify(cozeResult)}, ${collection_type || 'default'}, NOW())
      `
    } catch (logError) {
      console.error("记录成功日志时出错:", logError)
    }

    // 返回成功响应
    return NextResponse.json({
      message: "采集任务已提交",
      task_id: cozeResult.task_id,
      status: "pending"
    })

  } catch (error: any) {
    console.error("处理Coze采集请求时出错:", error)

    // 尝试记录异常到日志表
    try {
      if (request.body) {
        const body = await request.clone().json();
        await prisma.$queryRaw`
          INSERT INTO log_coze_invoke (id, user_id, invoke_param, resp_info, extend, created_at)
          VALUES (UUID(), ${user_id || '00000000-0000-0000-0000-000000000000'}, ${JSON.stringify(body)}, ${JSON.stringify({error: error.message})}, '内部服务器错误', NOW())
        `
      }
    } catch (logError) {
      console.error("记录错误日志失败:", logError)
    }

    return NextResponse.json({ error: "内部服务器错误" }, { status: 500 })
  }
}