"use client"

import { useState } from "react"
import { processImageWithOverlay } from "@/lib/image-processor"
import { But<PERSON> } from "@/components/ui/button"

export default function TestImageProcessorPage() {
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [processedImage, setProcessedImage] = useState<string | null>(null)
  const [processing, setProcessing] = useState(false)

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        setOriginalImage(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const processImage = async () => {
    if (!originalImage) return
    
    setProcessing(true)
    
    try {
      // 创建文件对象
      const response = await fetch(originalImage)
      const blob = await response.blob()
      const file = new File([blob], "test.jpg", { type: blob.type })
      
      // 处理图片
      const processedBlob = await processImageWithOverlay(
        file,
        {
          text: "爆款笔记",
          subText: "小红书封面\n一键生成",
          position: "center",
          fontSize: 80,
          color: "#ffffff",
          bgColor: "rgba(0,0,0,0.4)",
          fontWeight: "bold",
          textStyle: "outline",
          shadowColor: "#000000",
          includeDecorations: true
        },
        ["测试", "关键词", "标签"]
      )
      
      const url = URL.createObjectURL(processedBlob)
      setProcessedImage(url)
      
    } catch (error) {
      console.error("Error processing image:", error)
      alert("处理图片时出错：" + error)
    } finally {
      setProcessing(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">图片处理测试</h1>
      
      <div className="space-y-6">
        <div>
          <input 
            type="file" 
            accept="image/*" 
            onChange={handleImageUpload}
            className="mb-4"
          />
          
          <Button 
            onClick={processImage} 
            disabled={!originalImage || processing}
          >
            {processing ? "处理中..." : "处理图片"}
          </Button>
        </div>
        
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h2 className="text-lg font-semibold mb-2">原始图片</h2>
            {originalImage && (
              <img 
                src={originalImage} 
                alt="原始图片" 
                className="w-full border"
              />
            )}
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">处理后图片</h2>
            {processedImage && (
              <img 
                src={processedImage} 
                alt="处理后图片" 
                className="w-full border"
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}