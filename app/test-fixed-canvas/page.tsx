"use client"

import { useState, useEffect } from "react"
import { processImageWithOverlay } from "@/lib/image-processor"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export default function TestFixedCanvasPage() {
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [processedImages, setProcessedImages] = useState<{title: string, url: string}[]>([])
  const [processing, setProcessing] = useState(false)

  // 创建默认测试图片
  useEffect(() => {
    const canvas = document.createElement('canvas')
    canvas.width = 1200
    canvas.height = 800
    const ctx = canvas.getContext('2d')
    if (ctx) {
      // 创建渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 1200, 800)
      gradient.addColorStop(0, '#667eea')
      gradient.addColorStop(1, '#764ba2')
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, 1200, 800)
      
      canvas.toBlob((blob) => {
        if (blob) {
          setOriginalImage(URL.createObjectURL(blob))
        }
      })
    }
  }, [])

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        setOriginalImage(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const runTests = async () => {
    if (!originalImage) return
    
    setProcessing(true)
    setProcessedImages([])
    
    try {
      const response = await fetch(originalImage)
      const blob = await response.blob()
      const file = new File([blob], "test.jpg", { type: blob.type })
      
      const testCases = [
        {
          title: "中心位置 - 描边效果",
          overlay: {
            text: "爆款笔记",
            subText: "小红书封面\n一键生成",
            position: "center" as const,
            fontSize: 80,
            color: "#ffffff",
            bgColor: "transparent",
            fontWeight: "bold" as const,
            textStyle: "outline" as const,
            shadowColor: "#000000",
            includeDecorations: true
          }
        },
        {
          title: "顶部位置 - 渐变效果",
          overlay: {
            text: "美食探店",
            subText: "必吃榜单\n人均50元",
            position: "top-center" as const,
            fontSize: 96,
            color: "#ffeb3b",
            bgColor: "transparent",
            fontWeight: "bold" as const,
            textStyle: "gradient" as const,
            shadowColor: "#333333",
            includeDecorations: true
          }
        },
        {
          title: "底部位置 - 霓虹效果",
          overlay: {
            text: "旅行攻略",
            subText: "3天2夜\n完美行程",
            position: "bottom-center" as const,
            fontSize: 72,
            color: "#00bcd4",
            bgColor: "transparent",
            fontWeight: "normal" as const,
            textStyle: "neon" as const,
            shadowColor: "#004a5c",
            includeDecorations: false
          }
        },
        {
          title: "左上角 - 普通效果",
          overlay: {
            text: "生活分享",
            subText: "日常vlog\n记录美好",
            position: "top-left" as const,
            fontSize: 60,
            color: "#ffffff",
            bgColor: "transparent",
            fontWeight: "bold" as const,
            textStyle: "normal" as const,
            shadowColor: "#000000",
            includeDecorations: true
          }
        }
      ]
      
      const results = []
      for (const testCase of testCases) {
        console.log(`Processing: ${testCase.title}`)
        const processedBlob = await processImageWithOverlay(
          file,
          testCase.overlay,
          ["测试", "关键词", "标签", "小红书", "爆款"]
        )
        const url = URL.createObjectURL(processedBlob)
        results.push({ title: testCase.title, url })
      }
      
      setProcessedImages(results)
      
    } catch (error) {
      console.error("Error processing images:", error)
      alert("处理图片时出错：" + error)
    } finally {
      setProcessing(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">固定画布尺寸测试 (800x800)</h1>
      
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <input 
            type="file" 
            accept="image/*" 
            onChange={handleImageUpload}
            className="mb-4"
          />
          
          <Button 
            onClick={runTests} 
            disabled={!originalImage || processing}
          >
            {processing ? "处理中..." : "运行测试"}
          </Button>
        </div>
        
        <div className="space-y-6">
          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-2">原始图片</h2>
            {originalImage && (
              <img 
                src={originalImage} 
                alt="原始图片" 
                className="max-w-md border"
              />
            )}
          </Card>
          
          {processedImages.length > 0 && (
            <Card className="p-4">
              <h2 className="text-lg font-semibold mb-4">处理结果 (800x800 固定尺寸)</h2>
              <div className="grid grid-cols-2 gap-4">
                {processedImages.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <h3 className="font-medium">{item.title}</h3>
                    <img 
                      src={item.url} 
                      alt={item.title}
                      className="w-full border"
                      style={{ maxWidth: '400px' }}
                    />
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}