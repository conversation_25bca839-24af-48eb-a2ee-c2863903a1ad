{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "tsx scripts/full-supabase-to-mysql-migration.ts", "db:migrate-complete": "tsx scripts/complete-migrate-to-mysql.ts", "db:validate": "tsx scripts/validate-migration.ts", "db:migrate-safe": "tsx scripts/safe-migrate-to-mysql.ts", "db:reset": "prisma db push --force-reset", "db:test": "tsx scripts/test-mysql-repositories.ts", "db:sync-rewrite-queue": "tsx scripts/sync-rewrite-queue.ts", "db:sync-rewrite-queue-force": "tsx scripts/sync-rewrite-queue.ts --ignore-foreign-keys", "db:sync-publish-stats": "tsx scripts/sync-publish-plan-stats.ts", "db:sync-publish-status": "npx tsx scripts/sync-publish-plan-status.ts", "db:migrate-to-points": "tsx scripts/migrate-to-points-system.ts", "points:grant-monthly": "tsx scripts/grant-monthly-points.ts", "points:create-wallets": "tsx scripts/create-missing-wallets.ts", "restore:images": "tsx scripts/restore-note-images.ts", "restore:images-advanced": "tsx scripts/restore-note-images-advanced.ts", "update:content-images": "tsx scripts/update-existing-content-images.ts", "postinstall": "prisma generate"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@alicloud/credentials": "^2.4.3", "@alicloud/dm20151123": "^1.2.5", "@alicloud/openapi-client": "^0.4.14", "@alicloud/tea-typescript": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "@aws-sdk/client-s3": "^3.821.0", "@hookform/resolvers": "^3.9.1", "@next/third-parties": "^15.2.4", "@openrouter/ai-sdk-provider": "latest", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "latest", "@supabase/ssr": "^0.4.1", "@supabase/supabase-js": "latest", "@upstash/redis": "latest", "ai": "^2.2.25", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.0.0", "dotenv": "^16.5.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "mysql2": "^3.11.4", "next": "15.2.4", "next-themes": "latest", "openai": "^4.98.0", "qrcode.react": "^4.2.0", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "latest", "vaul": "^0.9.6", "zod": "latest"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "postcss": "^8", "prisma": "^5.22.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.2", "typescript": "^5"}}