"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, Palette } from "lucide-react"
import type { TextOverlay, UploadedImage } from "@/lib/types/image-batch-generator"
import { PRESET_STYLES } from "@/lib/types/image-batch-generator"

interface TextOverlayConfigProps {
  textOverlay: TextOverlay
  onTextOverlayChange: (overlay: TextOverlay) => void
  uploadedImages: UploadedImage[]
  previewImage: string | null
  onPreview: () => void
  batchMode: boolean
  autoVariation: boolean
  onApplyPreset?: (presetKey: string) => void
  selectedPreset?: string | null
}

export function TextOverlayConfig({
  textOverlay,
  onTextOverlayChange,
  uploadedImages,
  previewImage,
  onPreview,
  batchMode,
  autoVariation,
  onApplyPreset,
  selectedPreset
}: TextOverlayConfigProps) {
  return (
    <div className="space-y-4">
      {/* 预设样式 */}
      {onApplyPreset && (
        <div>
          <Label className="flex items-center gap-2 mb-2">
            <Palette className="h-4 w-4" />
            快捷样式
          </Label>
          <div className="grid grid-cols-3 gap-2">
            {Object.entries(PRESET_STYLES).map(([key, preset]) => (
              <Button
                key={key}
                size="sm"
                variant={selectedPreset === key ? "default" : "outline"}
                onClick={() => onApplyPreset(key)}
                className="text-xs"
              >
                {preset.name}
              </Button>
            ))}
          </div>
        </div>
      )}

      <div>
        <Label htmlFor="overlay-text">主标题</Label>
        <Textarea
          id="overlay-text"
          placeholder="输入要显示在封面上的主标题（留空则自动生成）"
          value={textOverlay.text}
          onChange={(e) => onTextOverlayChange({ ...textOverlay, text: e.target.value })}
          className="mt-1"
          rows={2}
        />
        {batchMode && autoVariation && !textOverlay.text && (
          <p className="text-xs text-muted-foreground mt-1">
            批量模式下将自动为每个笔记生成不同的花字文本
          </p>
        )}
      </div>

      <div>
        <Label htmlFor="sub-text">副标题</Label>
        <Textarea
          id="sub-text"
          placeholder="输入副标题或描述（可选）"
          value={textOverlay.subText || ""}
          onChange={(e) => onTextOverlayChange({ ...textOverlay, subText: e.target.value })}
          className="mt-1"
          rows={2}
        />
      </div>

      {uploadedImages.length > 0 && (
        <Button
          size="sm"
          variant="ghost"
          className="w-full"
          onClick={onPreview}
        >
          <Eye className="mr-2 h-4 w-4" />
          预览效果
        </Button>
      )}
      
      {/* 预览图片 */}
      {previewImage && (
        <div className="mt-4">
          <Label>预览效果</Label>
          <div className="mt-2 aspect-square bg-muted rounded-lg overflow-hidden">
            <img
              src={previewImage}
              alt="预览"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      )}
      
      <div>
        <Label htmlFor="text-style">文字样式</Label>
        <Select
          value={textOverlay.textStyle || "normal"}
          onValueChange={(value: any) => onTextOverlayChange({ ...textOverlay, textStyle: value })}
        >
          <SelectTrigger id="text-style" className="mt-1">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="normal">普通</SelectItem>
            <SelectItem value="bold">加粗</SelectItem>
            <SelectItem value="outline">描边</SelectItem>
            <SelectItem value="gradient">渐变</SelectItem>
            <SelectItem value="neon">霓虹</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="text-position">位置</Label>
        <Select
          value={textOverlay.position}
          onValueChange={(value: any) => onTextOverlayChange({ ...textOverlay, position: value })}
        >
          <SelectTrigger id="text-position" className="mt-1">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="top-left">左上</SelectItem>
            <SelectItem value="top-center">顶部居中</SelectItem>
            <SelectItem value="top-right">右上</SelectItem>
            <SelectItem value="center">居中</SelectItem>
            <SelectItem value="bottom-left">左下</SelectItem>
            <SelectItem value="bottom-center">底部居中</SelectItem>
            <SelectItem value="bottom-right">右下</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label htmlFor="font-size">字体大小</Label>
        <div className="flex items-center gap-2 mt-1">
          <Input
            id="font-size"
            type="number"
            min="24"
            max="200"
            step="4"
            value={textOverlay.fontSize}
            onChange={(e) => onTextOverlayChange({ ...textOverlay, fontSize: parseInt(e.target.value) || 96 })}
            className="flex-1"
          />
          <span className="text-sm text-muted-foreground">px</span>
        </div>
        <div className="flex gap-2 mt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onTextOverlayChange({ ...textOverlay, fontSize: 72 })}
          >
            小
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onTextOverlayChange({ ...textOverlay, fontSize: 96 })}
          >
            中
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onTextOverlayChange({ ...textOverlay, fontSize: 120 })}
          >
            大
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onTextOverlayChange({ ...textOverlay, fontSize: 144 })}
          >
            特大
          </Button>
        </div>
      </div>
      
      <div>
        <Label>字体样式</Label>
        <div className="flex gap-2 mt-2">
          <Button
            size="sm"
            variant={textOverlay.fontWeight === "normal" ? "default" : "outline"}
            onClick={() => onTextOverlayChange({ ...textOverlay, fontWeight: "normal" })}
          >
            常规
          </Button>
          <Button
            size="sm"
            variant={textOverlay.fontWeight === "bold" ? "default" : "outline"}
            onClick={() => onTextOverlayChange({ ...textOverlay, fontWeight: "bold" })}
          >
            加粗
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="text-color">文字颜色</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="text-color"
              type="color"
              value={textOverlay.color}
              onChange={(e) => onTextOverlayChange({ ...textOverlay, color: e.target.value })}
              className="h-10 w-20"
            />
            <div className="flex gap-1">
              <Button
                size="icon"
                variant="outline"
                className="h-10 w-10"
                onClick={() => onTextOverlayChange({ ...textOverlay, color: "#ffffff" })}
              >
                <div className="w-4 h-4 bg-white border rounded" />
              </Button>
              <Button
                size="icon"
                variant="outline"
                className="h-10 w-10"
                onClick={() => onTextOverlayChange({ ...textOverlay, color: "#000000" })}
              >
                <div className="w-4 h-4 bg-black rounded" />
              </Button>
              <Button
                size="icon"
                variant="outline"
                className="h-10 w-10"
                onClick={() => onTextOverlayChange({ ...textOverlay, color: "#ff0000" })}
              >
                <div className="w-4 h-4 bg-red-500 rounded" />
              </Button>
            </div>
          </div>
        </div>
        <div>
          <Label htmlFor="shadow-color">阴影颜色</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="shadow-color"
              type="color"
              value={textOverlay.shadowColor || "#000000"}
              onChange={(e) => onTextOverlayChange({ ...textOverlay, shadowColor: e.target.value })}
              className="h-10 w-20"
            />
            <div className="flex gap-1">
              <Button
                size="icon"
                variant="outline"
                className="h-10 w-10"
                onClick={() => onTextOverlayChange({ ...textOverlay, shadowColor: "#000000" })}
              >
                <div className="w-4 h-4 bg-black rounded" />
              </Button>
              <Button
                size="icon"
                variant="outline"
                className="h-10 w-10"
                onClick={() => onTextOverlayChange({ ...textOverlay, shadowColor: "#ffffff" })}
              >
                <div className="w-4 h-4 bg-white border rounded" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="bg-color">背景颜色</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="bg-color"
              type="color"
              value={textOverlay.bgColor.slice(0, 7)}
              onChange={(e) => onTextOverlayChange({ ...textOverlay, bgColor: e.target.value + "aa" })}
              className="h-10 w-20"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => onTextOverlayChange({ ...textOverlay, bgColor: "transparent" })}
            >
              透明
            </Button>
          </div>
        </div>
        <div>
          <Label>装饰元素</Label>
          <div className="flex gap-2 mt-1">
            <Button
              size="sm"
              variant={textOverlay.includeDecorations !== false ? "default" : "outline"}
              onClick={() => onTextOverlayChange({ ...textOverlay, includeDecorations: true })}
            >
              开启
            </Button>
            <Button
              size="sm"
              variant={textOverlay.includeDecorations === false ? "default" : "outline"}
              onClick={() => onTextOverlayChange({ ...textOverlay, includeDecorations: false })}
            >
              关闭
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}