"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Copy, Download, Save, X, Plus, FileText, 
  Trash2, Upload, Loader2, Image as ImageIcon, 
  ArrowUp, ArrowDown, Wand2, AlertCircle, Tag
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import type { GeneratedNote } from "@/lib/types/image-batch-generator"

interface NoteEditorV2Props {
  note: GeneratedNote | undefined
  noteIndex: number
  onUpdateNote: (updates: Partial<GeneratedNote>) => void
  onClose?: () => void
  onSave?: (note: GeneratedNote) => void
}

export function NoteEditorV2({ 
  note, 
  noteIndex,
  onUpdateNote,
  onClose,
  onSave
}: NoteEditorV2Props) {
  // 编辑状态
  const [editTitle, setEditTitle] = useState(note?.title || "")
  const [editContent, setEditContent] = useState(note?.content || "")
  const [contentImages, setContentImages] = useState<string[]>(note?.images || [])
  const [editCoverImage, setEditCoverImage] = useState<string | undefined>(note?.coverImage)
  const [tags, setTags] = useState<string[]>(note?.keywords || [])
  const [newTag, setNewTag] = useState("")
  const [newImageUrl, setNewImageUrl] = useState("")
  
  // UI状态
  const [activeTab, setActiveTab] = useState("content")
  const [isSaving, setIsSaving] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isUploadingCover, setIsUploadingCover] = useState(false)
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  const coverFileInputRef = useRef<HTMLInputElement>(null)

  // 同步props的变化
  useEffect(() => {
    if (note) {
      setEditTitle(note.title)
      setEditContent(note.content)
      setContentImages(note.images)
      setEditCoverImage(note.coverImage)
      setTags(note.keywords)
    }
  }, [note])

  // 从内容中提取标签
  useEffect(() => {
    const extractTags = () => {
      const tagRegex = /#([^\s#]+)/g
      const matches = editContent.match(tagRegex)
      if (matches) {
        const extractedTags = matches.map((tag) => tag.substring(1))
        setTags(extractedTags)
      }
    }
    extractTags()
  }, [editContent])

  if (!note) {
    return (
      <div className="flex-1 flex items-center justify-center text-muted-foreground">
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>点击左侧笔记进行查看和编辑</p>
        </div>
      </div>
    )
  }

  // 文件上传处理
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setIsUploading(true)
    try {
      const newImages: string[] = []
      
      for (const file of Array.from(files)) {
        // 这里应该调用实际的上传API
        // 现在我们只是创建本地预览URL
        const reader = new FileReader()
        const result = await new Promise<string>((resolve) => {
          reader.onload = (e) => resolve(e.target?.result as string)
          reader.readAsDataURL(file)
        })
        newImages.push(result)
      }
      
      const updatedImages = [...contentImages, ...newImages]
      setContentImages(updatedImages)
      onUpdateNote({ images: updatedImages })
      
      toast({
        title: "上传成功",
        description: `已上传 ${files.length} 张图片`
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "上传失败",
        description: "请稍后重试"
      })
    } finally {
      setIsUploading(false)
    }
  }

  // 封面图上传
  const handleCoverUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsUploadingCover(true)
    try {
      const reader = new FileReader()
      const result = await new Promise<string>((resolve) => {
        reader.onload = (e) => resolve(e.target?.result as string)
        reader.readAsDataURL(file)
      })
      
      setEditCoverImage(result)
      onUpdateNote({ coverImage: result })
      
      toast({
        title: "封面上传成功"
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "封面上传失败"
      })
    } finally {
      setIsUploadingCover(false)
    }
  }

  // 添加图片URL
  const handleAddImage = () => {
    if (!newImageUrl.trim()) return
    
    const updatedImages = [...contentImages, newImageUrl]
    setContentImages(updatedImages)
    onUpdateNote({ images: updatedImages })
    setNewImageUrl("")
  }

  // 删除图片
  const handleRemoveImage = (image: string) => {
    const updatedImages = contentImages.filter(img => img !== image)
    setContentImages(updatedImages)
    onUpdateNote({ images: updatedImages })
  }

  // 设置为封面
  const handleSelectCoverFromContent = (image: string) => {
    setEditCoverImage(image)
    onUpdateNote({ coverImage: image })
    toast({
      title: "已设为封面"
    })
  }

  // 移动图片顺序
  const moveImageUp = (index: number) => {
    if (index === 0) return
    const newImages = [...contentImages]
    ;[newImages[index], newImages[index - 1]] = [newImages[index - 1], newImages[index]]
    setContentImages(newImages)
    onUpdateNote({ images: newImages })
  }

  const moveImageDown = (index: number) => {
    if (index === contentImages.length - 1) return
    const newImages = [...contentImages]
    ;[newImages[index], newImages[index + 1]] = [newImages[index + 1], newImages[index]]
    setContentImages(newImages)
    onUpdateNote({ images: newImages })
  }

  // 添加标签
  const handleAddTag = () => {
    if (!newTag.trim() || tags.includes(newTag.trim())) return
    
    const updatedTags = [...tags, newTag.trim()]
    setTags(updatedTags)
    onUpdateNote({ keywords: updatedTags })
    setNewTag("")
  }

  // 删除标签
  const handleRemoveTag = (tag: string) => {
    const updatedTags = tags.filter(t => t !== tag)
    setTags(updatedTags)
    onUpdateNote({ keywords: updatedTags })
  }

  // 保存处理
  const handleSave = async () => {
    setIsSaving(true)
    
    try {
      // 更新所有内容
      onUpdateNote({
        title: editTitle,
        content: editContent,
        coverImage: editCoverImage,
        images: contentImages,
        keywords: tags
      })
      
      // 如果有保存回调，调用它
      if (onSave && note) {
        await onSave({
          ...note,
          title: editTitle,
          content: editContent,
          coverImage: editCoverImage || note.coverImage,
          images: contentImages,
          keywords: tags
        })
      }
      
      toast({
        title: "保存成功",
        description: "笔记已保存"
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "保存失败",
        description: "请稍后重试"
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 复制内容
  const handleCopy = () => {
    const content = `${editTitle}\n\n${editContent}`
    navigator.clipboard.writeText(content)
    toast({
      title: "复制成功",
      description: "笔记内容已复制到剪贴板"
    })
  }

  // 下载封面
  const handleDownload = () => {
    if (!note.coverImage) return
    
    const link = document.createElement('a')
    link.href = note.coverImage
    link.download = `note_${noteIndex + 1}_cover.jpg`
    link.click()
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-3xl font-bold tracking-tight">编辑笔记</h2>
          <Badge variant="outline">#{noteIndex + 1}</Badge>
        </div>
        {onClose && (
          <Button variant="outline" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </Button>
        )}
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle>笔记编辑器</CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopy}
            >
              <Copy className="mr-2 h-4 w-4" />
              复制文案
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              disabled={!note.coverImage}
            >
              <Download className="mr-2 h-4 w-4" />
              下载封面
            </Button>
            <Button 
              variant="default" 
              size="sm" 
              onClick={handleSave} 
              disabled={isSaving}
            >
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? "保存中..." : "保存"}
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content">内容编辑</TabsTrigger>
              <TabsTrigger value="images">图片管理</TabsTrigger>
              <TabsTrigger value="preview">预览效果</TabsTrigger>
            </TabsList>

            {/* 内容编辑标签页 */}
            <TabsContent value="content" className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">标题</Label>
                <Input 
                  value={editTitle} 
                  onChange={(e) => {
                    setEditTitle(e.target.value)
                    onUpdateNote({ title: e.target.value })
                  }} 
                  placeholder="输入笔记标题" 
                />
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">内容</Label>
                <Textarea
                  value={editContent}
                  onChange={(e) => {
                    setEditContent(e.target.value)
                    onUpdateNote({ content: e.target.value })
                  }}
                  placeholder="输入笔记内容"
                  className="min-h-[300px] resize-none"
                />
                <p className="text-sm text-muted-foreground text-right">
                  {editContent.length} 字
                </p>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">标签</Label>
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="添加标签"
                    onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
                  />
                  <Button onClick={handleAddTag} type="button">
                    <Plus className="h-4 w-4 mr-2" />
                    添加
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {tags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="gap-1"
                    >
                      <Tag className="h-3 w-3" />
                      #{tag}
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:bg-secondary rounded"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* 图片管理标签页 */}
            <TabsContent value="images" className="space-y-4">
              <div className="space-y-4">
                {/* 封面图 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">封面图</Label>
                  <div className="flex gap-4">
                    <div className="aspect-square w-[200px] rounded-md overflow-hidden border relative group">
                      <img
                        src={editCoverImage || "/placeholder.svg"}
                        alt="封面图"
                        className="w-full h-full object-cover"
                      />
                      {editCoverImage && (
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => {
                            setEditCoverImage(undefined)
                            onUpdateNote({ coverImage: undefined })
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      <input
                        type="file"
                        ref={coverFileInputRef}
                        accept="image/*"
                        className="hidden"
                        onChange={handleCoverUpload}
                      />
                      <Button
                        variant="outline"
                        onClick={() => coverFileInputRef.current?.click()}
                        disabled={isUploadingCover}
                      >
                        {isUploadingCover ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            上传中...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            上传封面
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 内容图片 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">内容图片</Label>
                  
                  {/* 上传控件 */}
                  <div className="flex flex-col gap-2">
                    <div className="flex gap-2">
                      <input
                        type="file"
                        ref={fileInputRef}
                        accept="image/*"
                        multiple
                        className="hidden"
                        onChange={handleFileUpload}
                      />
                      <Button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                        className="flex-1"
                      >
                        {isUploading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            上传中...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            选择文件上传
                          </>
                        )}
                      </Button>
                    </div>
                    
                    <div className="flex gap-2">
                      <Input
                        value={newImageUrl}
                        onChange={(e) => setNewImageUrl(e.target.value)}
                        placeholder="或输入图片URL"
                        className="flex-1"
                      />
                      <Button onClick={handleAddImage} disabled={!newImageUrl}>
                        <Plus className="h-4 w-4 mr-2" />
                        添加
                      </Button>
                    </div>
                  </div>

                  {/* 图片列表 */}
                  {contentImages.length === 0 ? (
                    <p className="text-sm text-muted-foreground">暂无内容图片</p>
                  ) : (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {contentImages.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image}
                            alt={`内容图片 ${index + 1}`}
                            className="w-full aspect-square object-cover rounded-md border"
                          />
                          <div className="absolute top-2 right-2 space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              variant="secondary"
                              size="icon"
                              className="h-6 w-6"
                              title="设为封面"
                              onClick={() => handleSelectCoverFromContent(image)}
                            >
                              <ImageIcon className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="destructive"
                              size="icon"
                              className="h-6 w-6"
                              title="删除图片"
                              onClick={() => handleRemoveImage(image)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="absolute left-2 top-2 flex flex-col space-y-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              variant="secondary"
                              size="icon"
                              className="h-6 w-6"
                              title="上移"
                              onClick={() => moveImageUp(index)}
                              disabled={index === 0}
                            >
                              <ArrowUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="secondary"
                              size="icon"
                              className="h-6 w-6"
                              title="下移"
                              onClick={() => moveImageDown(index)}
                              disabled={index === contentImages.length - 1}
                            >
                              <ArrowDown className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                            {index + 1}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* 预览标签页 */}
            <TabsContent value="preview" className="space-y-4">
              <div className="max-w-[400px] mx-auto">
                <div className="bg-white rounded-lg shadow-sm border">
                  {/* 封面图 */}
                  {editCoverImage && (
                    <div className="aspect-[4/3] overflow-hidden rounded-t-lg">
                      <img
                        src={editCoverImage}
                        alt="封面预览"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  
                  {/* 内容 */}
                  <div className="p-4 space-y-3">
                    <h3 className="font-semibold text-lg line-clamp-2">
                      {editTitle || "无标题"}
                    </h3>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap line-clamp-5">
                      {editContent || "无内容"}
                    </p>
                    
                    {/* 标签 */}
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {tags.map((tag, index) => (
                          <span key={index} className="text-xs text-primary">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    {/* 图片数量 */}
                    {contentImages.length > 0 && (
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <ImageIcon className="h-3 w-3" />
                        {contentImages.length} 张图片
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}