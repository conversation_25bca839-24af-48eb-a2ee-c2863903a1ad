"use client"

import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download } from "lucide-react"

interface SingleGalleryResultProps {
  galleryComposite: string | null
  generatedGallery: string[] | null
}

export function SingleGalleryResult({ 
  galleryComposite, 
  generatedGallery 
}: SingleGalleryResultProps) {
  if (!galleryComposite) return null

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">生成结果</h2>
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => {
              const link = document.createElement('a')
              link.href = galleryComposite
              link.download = `gallery_${Date.now()}.jpg`
              link.click()
            }}
          >
            <Download className="mr-2 h-4 w-4" />
            下载组合图
          </Button>
        </div>
      </div>
      <div className="aspect-[4/3] bg-muted rounded-lg overflow-hidden">
        <img
          src={galleryComposite}
          alt="生成的图集"
          className="w-full h-full object-contain"
        />
      </div>
      
      {/* 单独显示所有图片 */}
      {generatedGallery && generatedGallery.length > 0 && (
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">单独图片</h3>
          <div className="grid grid-cols-3 gap-2">
            {generatedGallery.map((url, index) => (
              <div key={index} className="aspect-square bg-muted rounded overflow-hidden">
                <img src={url} alt={`图片 ${index + 1}`} className="w-full h-full object-cover" />
              </div>
            ))}
          </div>
        </div>
      )}
    </Card>
  )
}