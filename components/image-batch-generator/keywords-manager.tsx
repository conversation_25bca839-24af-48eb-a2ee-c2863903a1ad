"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, X } from "lucide-react"

interface KeywordsManagerProps {
  keywords: string[]
  onKeywordsChange: (keywords: string[]) => void
}

export function KeywordsManager({ keywords, onKeywordsChange }: KeywordsManagerProps) {
  const [currentKeyword, setCurrentKeyword] = useState("")

  const addKeyword = () => {
    if (currentKeyword.trim() && keywords.length < 10) {
      onKeywordsChange([...keywords, currentKeyword.trim()])
      setCurrentKeyword("")
    }
  }

  const removeKeyword = (index: number) => {
    onKeywordsChange(keywords.filter((_, i) => i !== index))
  }

  return (
    <div className="space-y-4">
      <div>
        <Label>添加关键词标签</Label>
        <div className="flex gap-2 mt-1">
          <Input
            placeholder="输入关键词"
            value={currentKeyword}
            onChange={(e) => setCurrentKeyword(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && addKeyword()}
          />
          <Button size="icon" onClick={addKeyword}>
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {keywords.map((keyword, index) => (
          <div
            key={index}
            className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center gap-1"
          >
            #{keyword}
            <button
              onClick={() => removeKeyword(index)}
              className="hover:bg-primary/20 rounded-full p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        ))}
      </div>
    </div>
  )
}