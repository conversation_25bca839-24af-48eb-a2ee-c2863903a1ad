"use client"

import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import type { GeneratorConfig } from "@/lib/types/image-batch-generator"

interface AdvancedSettingsProps {
  config: GeneratorConfig
  onConfigChange: (config: Partial<GeneratorConfig>) => void
}

export function AdvancedSettings({ config, onConfigChange }: AdvancedSettingsProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label htmlFor="batch-mode">批量生成模式</Label>
        <Switch
          id="batch-mode"
          checked={config.batchMode}
          onCheckedChange={(batchMode) => onConfigChange({ batchMode })}
        />
      </div>
      
      {config.batchMode && (
        <div>
          <Label htmlFor="generation-count">生成数量</Label>
          <div className="flex items-center gap-4 mt-2">
            <Slider
              id="generation-count"
              min={1}
              max={30}
              step={1}
              value={[config.generationCount]}
              onValueChange={(value) => onConfigChange({ generationCount: value[0] })}
              className="flex-1"
            />
            <Input
              type="number"
              min={1}
              max={30}
              value={config.generationCount}
              onChange={(e) => onConfigChange({ generationCount: parseInt(e.target.value) || 10 })}
              className="w-20"
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            将生成 {config.generationCount} 个不同的笔记
          </p>
        </div>
      )}

      <div className="flex items-center justify-between">
        <Label htmlFor="auto-variation">自动变化</Label>
        <Switch
          id="auto-variation"
          checked={config.autoVariation}
          onCheckedChange={(autoVariation) => onConfigChange({ autoVariation })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="auto-title">自动生成标题</Label>
        <Switch
          id="auto-title"
          checked={config.autoGenerateTitle}
          onCheckedChange={(autoGenerateTitle) => onConfigChange({ autoGenerateTitle })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="watermark">添加水印</Label>
        <Switch
          id="watermark"
          checked={config.includeWatermark}
          onCheckedChange={(includeWatermark) => onConfigChange({ includeWatermark })}
        />
      </div>
      
      <div>
        <Label htmlFor="quality">图片质量</Label>
        <Select 
          value={config.imageQuality} 
          onValueChange={(imageQuality) => onConfigChange({ imageQuality })}
        >
          <SelectTrigger id="quality" className="mt-1">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">低质量（快速）</SelectItem>
            <SelectItem value="medium">中等质量</SelectItem>
            <SelectItem value="high">高质量</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label htmlFor="template">模板风格</Label>
        <Select 
          value={config.selectedTemplate} 
          onValueChange={(selectedTemplate) => onConfigChange({ selectedTemplate })}
        >
          <SelectTrigger id="template" className="mt-1">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">默认风格</SelectItem>
            <SelectItem value="minimal">简约风格</SelectItem>
            <SelectItem value="magazine">杂志风格</SelectItem>
            <SelectItem value="artistic">艺术风格</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}