"use client"

import { NotesList } from "./notes-list"
import { NoteEditorSimplified } from "./note-editor-simplified"
import type { GeneratedNote } from "@/lib/types/image-batch-generator"

interface BatchGenerationResultsProps {
  notes: GeneratedNote[]
  selectedNoteId: string | null
  onSelectNote: (noteId: string) => void
  onClose: () => void
  onUpdateNote: (noteId: string, updates: Partial<GeneratedNote>) => void
  onSaveToDatabase: () => void
  isSaving: boolean
}

export function BatchGenerationResults({
  notes,
  selectedNoteId,
  onSelectNote,
  onClose,
  onUpdateNote,
  onSaveToDatabase,
  isSaving
}: BatchGenerationResultsProps) {
  const selectedNote = notes.find(note => note.id === selectedNoteId)
  const selectedNoteIndex = notes.findIndex(note => note.id === selectedNoteId)

  const handleDownloadAll = () => {
    notes.forEach((note, index) => {
      const link = document.createElement('a')
      link.href = note.coverImage
      link.download = `note_${index + 1}_cover.jpg`
      link.click()
    })
  }

  const handleUpdateSelectedNote = (updates: Partial<GeneratedNote>) => {
    if (selectedNoteId) {
      onUpdateNote(selectedNoteId, updates)
    }
  }

  const handleNavigate = (direction: 'prev' | 'next') => {
    const currentIndex = notes.findIndex(note => note.id === selectedNoteId)
    if (currentIndex === -1) return

    const newIndex = direction === 'prev' ? currentIndex - 1 : currentIndex + 1
    if (newIndex >= 0 && newIndex < notes.length) {
      onSelectNote(notes[newIndex].id)
    }
  }

  return (
    <div className="fixed inset-0 bg-background z-50 flex">
      <NotesList
        notes={notes}
        selectedNoteId={selectedNoteId}
        onSelectNote={onSelectNote}
        onClose={onClose}
        onDownloadAll={handleDownloadAll}
        onSaveToDatabase={onSaveToDatabase}
        isSaving={isSaving}
      />
      
      <div className="flex-1 flex flex-col bg-background">
        <NoteEditorSimplified
          note={selectedNote}
          noteIndex={selectedNoteIndex}
          onUpdateNote={handleUpdateSelectedNote}
          onNavigate={handleNavigate}
          canNavigate={{
            prev: selectedNoteIndex > 0,
            next: selectedNoteIndex < notes.length - 1
          }}
        />
      </div>
    </div>
  )
}