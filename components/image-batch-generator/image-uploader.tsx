"use client"

import { useRef } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, X } from "lucide-react"
import type { UploadedImage } from "@/lib/types/image-batch-generator"

interface ImageUploaderProps {
  uploadedImages: UploadedImage[]
  onImagesChange: (images: UploadedImage[]) => void
}

export function ImageUploader({ uploadedImages, onImagesChange }: ImageUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files) return

    const newImages: UploadedImage[] = []
    Array.from(files).forEach((file, index) => {
      const reader = new FileReader()
      reader.onloadend = () => {
        newImages.push({
          id: Date.now().toString() + index,
          file,
          preview: reader.result as string,
          order: uploadedImages.length + index
        })
        
        if (newImages.length === files.length) {
          onImagesChange([...uploadedImages, ...newImages])
        }
      }
      reader.readAsDataURL(file)
    })
  }

  const removeImage = (id: string) => {
    onImagesChange(uploadedImages.filter(img => img.id !== id))
  }

  const reorderImages = (draggedId: string, targetId: string) => {
    const draggedIndex = uploadedImages.findIndex(img => img.id === draggedId)
    const targetIndex = uploadedImages.findIndex(img => img.id === targetId)
    
    if (draggedIndex === -1 || targetIndex === -1) return

    const newImages = [...uploadedImages]
    const [draggedImage] = newImages.splice(draggedIndex, 1)
    newImages.splice(targetIndex, 0, draggedImage)
    
    onImagesChange(newImages.map((img, index) => ({ ...img, order: index })))
  }

  return (
    <Card className="p-6">
      <h2 className="text-lg font-semibold mb-4">上传图片</h2>
      
      <div className="space-y-4">
        <div
          className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-sm text-muted-foreground">
            点击或拖拽图片到此处上传
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            支持 JPG、PNG、GIF 格式，单张最大 10MB
          </p>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />

        {/* 已上传图片列表 */}
        {uploadedImages.length > 0 && (
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4">
            {uploadedImages
              .sort((a, b) => a.order - b.order)
              .map((image, index) => (
                <div
                  key={image.id}
                  className="relative group aspect-square"
                  draggable
                  onDragStart={(e) => {
                    e.dataTransfer.setData("imageId", image.id)
                  }}
                  onDragOver={(e) => e.preventDefault()}
                  onDrop={(e) => {
                    e.preventDefault()
                    const draggedId = e.dataTransfer.getData("imageId")
                    reorderImages(draggedId, image.id)
                  }}
                >
                  <img
                    src={image.preview}
                    alt={`上传的图片 ${index + 1}`}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="text-white hover:bg-white/20"
                      onClick={() => removeImage(image.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  {index === 0 && (
                    <div className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                      封面
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}
      </div>
    </Card>
  )
}