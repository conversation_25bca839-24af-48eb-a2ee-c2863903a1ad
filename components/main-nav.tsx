"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { BookOpen, Send, Settings, Home, MessageSquare, Shield, Crown, MessageSquarePlus, Download, Sparkles, Package, TrendingUp, Images } from "lucide-react"
import { useCurrentUser, useIsAdmin } from "@/hooks/use-better-auth"

// 基础导航项
const baseNavItems = [
  {
    title: "仪表盘",
    href: "/dashboard",
    icon: Home,
  },
  {
    title: "笔记素材库",
    href: "/dashboard/notes",
    icon: BookOpen,
  },
  {
    title: "素材库管理",
    href: "/dashboard/materials",
    icon: Package,
  },
  {
    title: "笔记提取",
    href: "/dashboard/extract",
    icon: Download,
  },
  {
    title: "爆款笔记生成",
    href: "/dashboard/viral-note-generator",
    icon: Sparkles,
  },
  {
    title: "模板爆款笔记生成",
    href: "/dashboard/template-viral-note-generator",
    icon: Sparkles,
  },
  {
    title: "批量图集生成",
    href: "/dashboard/image-batch-generator",
    icon: Images,
  },
  {
    title: "品牌评论",
    href: "/dashboard/brandcomment",
    icon: MessageSquare,
  },
  {
    title: "舆情分析",
    href: "/dashboard/sentiment-analysis",
    icon: TrendingUp,
  },
  // {
  //   title: "评论分析",
  //   href: "/dashboard/comments",
  //   icon: MessageSquare,
  // },
  {
    title: "发布管理",
    href: "/dashboard/publish",
    icon: Send,
  },
  {
    title: "AI运营专家",
    href: "/dashboard/ai-expert",
    icon: MessageSquarePlus,
  },
  {
    title: "设置",
    href: "/dashboard/settings",
    icon: Settings,
  },
]

// 管理员导航项
const adminNavItems = [
  {
    title: "管理后台",
    href: "/dashboard/admin",
    icon: Shield,
  }
]

// 代理导航项
const agentNavItems = [
  {
    title: "管理后台",
    href: "/dashboard/admin",
    icon: Crown,
  }
]

interface MainNavProps {
  collapsed?: boolean
  onNavigate?: () => void
}

export function MainNav({ collapsed = false, onNavigate }: MainNavProps) {
  const pathname = usePathname()
  const { user } = useCurrentUser()
  const isAdmin = useIsAdmin()

  // 根据用户角色合并导航项
  let managementNavItems: any[] = []
  if (isAdmin) {
    managementNavItems = adminNavItems
  } else if (user?.role === 'agent') {
    managementNavItems = agentNavItems
  }

  const navItems = [...baseNavItems, ...managementNavItems]

  return (
    <nav className="px-2 w-full">
      {navItems.map((item) => {
        const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`)

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex h-10 items-center rounded-lg my-1 transition-all hover:text-primary",
              isActive ? "bg-primary/10 text-primary" : "text-muted-foreground hover:bg-muted",
              collapsed ? "justify-center w-10 mx-auto px-0" : "px-3 gap-3",
            )}
            title={collapsed ? item.title : undefined}
            onClick={() => {
              if (onNavigate) onNavigate()
            }}
          >
            <item.icon className={cn("h-5 w-5", isActive ? "text-primary" : "text-muted-foreground")} />
            {!collapsed && <span className="text-sm font-medium">{item.title}</span>}
          </Link>
        )
      })}
    </nav>
  )
}
