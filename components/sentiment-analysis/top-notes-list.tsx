import { Card } from "@/components/ui/card"
import { ThumbsUp, MessageSquare, Bookmark } from "lucide-react"

interface TopNotesListProps {
  data: any
}

export function TopNotesList({ data }: TopNotesListProps) {
  const topNotes = data.topNotesData || []

  if (topNotes.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">热门笔记排行</h3>
        <div className="text-center text-muted-foreground py-8">
          暂无笔记数据
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">热门笔记排行 Top 10</h3>
      <div className="space-y-3">
        {topNotes.map((note: any, index: number) => (
          <div key={note.id} className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-sm font-semibold text-primary">{index + 1}</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{note.title}</p>
              <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <ThumbsUp className="h-3 w-3" />
                  {note.likes.toLocaleString()}
                </span>
                <span className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  {note.comments.toLocaleString()}
                </span>
                <span className="flex items-center gap-1">
                  <Bookmark className="h-3 w-3" />
                  {note.saves.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  )
}