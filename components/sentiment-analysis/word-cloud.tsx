"use client"

import { Card } from "@/components/ui/card"
import { useEffect, useRef } from "react"

interface WordCloudProps {
  data: any
}

export function WordCloud({ data }: WordCloudProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const keywords = data.keywordsCloud || []

  useEffect(() => {
    if (!canvasRef.current || keywords.length === 0) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸
    const rect = canvas.getBoundingClientRect()
    canvas.width = rect.width
    canvas.height = rect.height

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 计算最大和最小词频
    const maxValue = Math.max(...keywords.map((k: any) => k.value))
    const minValue = Math.min(...keywords.map((k: any) => k.value))

    // 颜色数组
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899']

    // 简单的词云布局
    const positions: Array<{x: number, y: number, width: number, height: number}> = []
    
    keywords.slice(0, 30).forEach((keyword: any, index: number) => {
      // 计算字体大小
      const fontSize = 14 + ((keyword.value - minValue) / (maxValue - minValue)) * 36
      ctx.font = `${fontSize}px sans-serif`
      
      // 计算文本尺寸
      const metrics = ctx.measureText(keyword.text)
      const textWidth = metrics.width
      const textHeight = fontSize

      // 随机位置（简单布局）
      let x, y
      let attempts = 0
      let placed = false

      while (!placed && attempts < 50) {
        x = Math.random() * (canvas.width - textWidth)
        y = Math.random() * (canvas.height - textHeight) + textHeight

        // 检查碰撞
        let collision = false
        for (const pos of positions) {
          if (
            x < pos.x + pos.width &&
            x + textWidth > pos.x &&
            y - textHeight < pos.y &&
            y > pos.y - pos.height
          ) {
            collision = true
            break
          }
        }

        if (!collision) {
          placed = true
          positions.push({ x, y, width: textWidth, height: textHeight })
        }
        attempts++
      }

      if (placed) {
        // 绘制文本
        ctx.fillStyle = colors[index % colors.length]
        ctx.fillText(keyword.text, x!, y!)
      }
    })
  }, [keywords])

  if (keywords.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">关键词云</h3>
        <div className="text-center text-muted-foreground py-8">
          暂无关键词数据
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">关键词云</h3>
      <div className="relative w-full h-[400px]">
        <canvas 
          ref={canvasRef} 
          className="w-full h-full"
          style={{ width: '100%', height: '100%' }}
        />
      </div>
      <div className="mt-4 flex flex-wrap gap-2">
        {keywords.slice(0, 10).map((keyword: any, index: number) => (
          <div key={index} className="text-sm">
            <span className="font-medium">{keyword.text}</span>
            <span className="text-muted-foreground ml-1">({keyword.value})</span>
          </div>
        ))}
      </div>
    </Card>
  )
}