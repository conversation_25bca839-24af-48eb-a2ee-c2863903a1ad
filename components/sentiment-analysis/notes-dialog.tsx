"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, ThumbsUp, MessageSquare, Bookmark, ExternalLink } from "lucide-react"
import { Button } from "@/components/ui/button"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

interface Note {
  id: string
  noteId: string
  title: string
  content: string | null
  coverImage: string | null
  likes: number
  commentsCount: number
  saves: number
  publishedAt: Date | null
  url: string | null
  confidence: number
  keywords: string[]
}

interface NotesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cacheId: string
  sentiment: 'positive' | 'negative' | 'neutral'
  sentimentLabel: string
}

export function NotesDialog({ 
  open, 
  onOpenChange, 
  cacheId, 
  sentiment,
  sentimentLabel 
}: NotesD<PERSON>ogProps) {
  const [notes, setNotes] = useState<Note[]>([])
  const [loading, setLoading] = useState(false)
  const [totalCount, setTotalCount] = useState(0)

  useEffect(() => {
    if (open && cacheId) {
      fetchNotes()
    }
  }, [open, cacheId, sentiment])

  const fetchNotes = async () => {
    setLoading(true)
    try {
      const res = await fetch(`/api/sentiment-analysis/notes?cacheId=${cacheId}&sentiment=${sentiment}`)
      if (res.ok) {
        const data = await res.json()
        setNotes(data.notes)
        setTotalCount(data.totalCount)
      }
    } catch (error) {
      console.error("Error fetching notes:", error)
    } finally {
      setLoading(false)
    }
  }

  const getSentimentColor = () => {
    switch (sentiment) {
      case 'positive':
        return 'text-green-600 bg-green-50'
      case 'negative':
        return 'text-red-600 bg-red-50'
      case 'neutral':
        return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSentimentColor()}`}>
              {sentimentLabel}
            </span>
            <span className="text-muted-foreground">
              ({totalCount} 篇笔记)
            </span>
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <ScrollArea className="h-[60vh] pr-4">
            <div className="space-y-4">
              {notes.map((note) => (
                <Card key={note.id} className="p-4 hover:shadow-md transition-shadow">
                  <div className="flex gap-4">
                    {/* 封面图 */}
                    {note.coverImage && (
                      <div className="flex-shrink-0">
                        <img
                          src={note.coverImage}
                          alt={note.title}
                          className="w-24 h-24 object-cover rounded-lg"
                        />
                      </div>
                    )}
                    
                    {/* 内容 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <h3 className="font-semibold text-base line-clamp-2 flex-1">
                          {note.title}
                        </h3>
                        {note.url && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="flex-shrink-0"
                            onClick={() => window.open(note.url || '', '_blank')}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      
                      {note.content && (
                        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                          {note.content}
                        </p>
                      )}
                      
                      {/* 关键词 */}
                      {note.keywords && note.keywords.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {note.keywords.slice(0, 5).map((keyword, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      )}
                      
                      {/* 统计数据 */}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <ThumbsUp className="h-3 w-3" />
                          {note.likes.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          {note.commentsCount.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <Bookmark className="h-3 w-3" />
                          {note.saves.toLocaleString()}
                        </span>
                        {note.publishedAt && (
                          <span className="ml-auto">
                            {format(new Date(note.publishedAt), 'MM月dd日', { locale: zhCN })}
                          </span>
                        )}
                        <Badge variant="outline" className="text-xs">
                          置信度: {(note.confidence * 100).toFixed(0)}%
                        </Badge>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
              
              {notes.length === 0 && (
                <div className="text-center py-12 text-muted-foreground">
                  暂无{sentimentLabel}笔记
                </div>
              )}
            </div>
          </ScrollArea>
        )}
      </DialogContent>
    </Dialog>
  )
}