"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { Loader2, FileText, RefreshCw } from "lucide-react"
import ReactMarkdown from "react-markdown"

interface AIReportProps {
  cacheId: string
}

export function AIReport({ cacheId }: AIReportProps) {
  const [report, setReport] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [cached, setCached] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadReport()
  }, [cacheId])

  const loadReport = async () => {
    setLoading(true)
    try {
      const res = await fetch("/api/sentiment-analysis/report", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ cacheId })
      })

      if (!res.ok) {
        throw new Error("Failed to generate report")
      }

      const data = await res.json()
      
      if (!res.ok) {
        if (res.status === 402) {
          toast({
            title: "积分不足",
            description: data.error || "请充值后继续使用",
            variant: "destructive"
          })
        } else {
          throw new Error(data.error || "Failed to generate report")
        }
        return
      }
      
      setReport(data.report)
      setCached(data.cached)
    } catch (error) {
      console.error("Error loading report:", error)
      toast({
        title: "加载失败",
        description: "无法生成AI分析报告",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const regenerateReport = async () => {
    setLoading(true)
    setCached(false)
    try {
      const res = await fetch("/api/sentiment-analysis/report", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ cacheId, forceRefresh: true })
      })

      if (!res.ok) {
        throw new Error("Failed to regenerate report")
      }

      const data = await res.json()
      
      if (!res.ok) {
        if (res.status === 402) {
          toast({
            title: "积分不足",
            description: data.error || "请充值后继续使用",
            variant: "destructive"
          })
        } else {
          throw new Error(data.error || "Failed to regenerate report")
        }
        return
      }
      
      setReport(data.report)
      toast({
        title: "生成成功",
        description: "AI分析报告已重新生成"
      })
    } catch (error) {
      console.error("Error regenerating report:", error)
      toast({
        title: "生成失败",
        description: "无法重新生成报告",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading && !report) {
    return (
      <Card className="p-12">
        <div className="text-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
          <h3 className="text-lg font-semibold">正在生成AI分析报告...</h3>
          <p className="text-muted-foreground">
            AI正在分析数据并生成专业的舆情分析报告
          </p>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          <h3 className="text-lg font-semibold">AI智能分析报告</h3>
          {cached && (
            <span className="text-xs text-muted-foreground">(缓存)</span>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={regenerateReport}
          disabled={loading}
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          重新生成
        </Button>
      </div>

      {report ? (
        <div className="prose prose-sm max-w-none dark:prose-invert">
          <ReactMarkdown>{report}</ReactMarkdown>
        </div>
      ) : (
        <div className="text-center text-muted-foreground py-8">
          暂无报告内容
        </div>
      )}
    </Card>
  )
}