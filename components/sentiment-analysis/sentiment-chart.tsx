"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, <PERSON>ltip, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid } from "recharts"
import { NotesDialog } from "./notes-dialog"
import { SentimentOverviewButtons } from "./sentiment-overview-buttons"
import { PieChartWithHover } from "./pie-chart-with-hover"

interface SentimentChartProps {
  data: any
  detailed?: boolean
}

export function SentimentChart({ data, detailed = false }: SentimentChartProps) {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedSentiment, setSelectedSentiment] = useState<'positive' | 'negative' | 'neutral'>('positive')
  const [selectedLabel, setSelectedLabel] = useState('')
  
  const handleSentimentClick = (sentiment: 'positive' | 'negative' | 'neutral', label: string) => {
    setSelectedSentiment(sentiment)
    setSelectedLabel(label)
    setDialogOpen(true)
  }
  const sentimentData = data.sentimentDistribution || {}
  
  const notesPieData = [
    { name: "正面", value: data.positiveNotes || 0, color: "#10b981" },
    { name: "负面", value: data.negativeNotes || 0, color: "#ef4444" },
    { name: "中性", value: data.neutralNotes || 0, color: "#6b7280" }
  ].filter(item => item.value > 0)

  const commentsPieData = [
    { name: "正面", value: data.positiveComments || 0, color: "#10b981" },
    { name: "负面", value: data.negativeComments || 0, color: "#ef4444" },
    { name: "中性", value: data.neutralComments || 0, color: "#6b7280" }
  ].filter(item => item.value > 0)

  const barData = [
    {
      name: "笔记",
      正面: data.positiveNotes || 0,
      负面: data.negativeNotes || 0,
      中性: data.neutralNotes || 0
    }
  ]

  if (!detailed) {
    return (
      <>
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">情绪分布概览</h3>
            <span className="text-xs text-muted-foreground">点击柱状图查看详情</span>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={barData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="正面" fill="#10b981" cursor="pointer" onClick={() => handleSentimentClick('positive', '正面笔记')} />
              <Bar dataKey="负面" fill="#ef4444" cursor="pointer" onClick={() => handleSentimentClick('negative', '负面笔记')} />
              <Bar dataKey="中性" fill="#6b7280" cursor="pointer" onClick={() => handleSentimentClick('neutral', '中性笔记')} />
            </BarChart>
          </ResponsiveContainer>
          
          {/* 情绪选择按钮 */}
          <div className="mt-6">
            <SentimentOverviewButtons
              positiveNotes={data.positiveNotes || 0}
              negativeNotes={data.negativeNotes || 0}
              neutralNotes={data.neutralNotes || 0}
              onClick={handleSentimentClick}
            />
          </div>
        </Card>
        
        <NotesDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          cacheId={data.id}
          sentiment={selectedSentiment}
          sentimentLabel={selectedLabel}
        />
      </>
    )
  }

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">笔记情绪分布</h3>
          <span className="text-xs text-muted-foreground">点击饼图或下方按钮查看详情</span>
        </div>
        <PieChartWithHover
          data={notesPieData}
          onClick={(data) => {
            const sentimentMap = { '正面': 'positive', '负面': 'negative', '中性': 'neutral' } as const
            handleSentimentClick(sentimentMap[data.name as keyof typeof sentimentMap], `${data.name}笔记`)
          }}
          interactive={true}
        />
        <div className="mt-4 space-y-2">
          {notesPieData.map((item, index) => (
            <div 
              key={index} 
              className="flex items-center justify-between text-sm cursor-pointer hover:bg-muted/50 p-2 rounded-md transition-colors"
              onClick={() => {
                const sentimentMap = { '正面': 'positive', '负面': 'negative', '中性': 'neutral' } as const
                handleSentimentClick(sentimentMap[item.name as keyof typeof sentimentMap], item.name)
              }}
            >
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: item.color }} />
                <span>{item.name}</span>
              </div>
              <span className="font-semibold">{item.value}篇</span>
            </div>
          ))}
        </div>
      </Card>
    
    <NotesDialog
      open={dialogOpen}
      onOpenChange={setDialogOpen}
      cacheId={data.id}
      sentiment={selectedSentiment}
      sentimentLabel={selectedLabel}
    />
  </>
  )
}