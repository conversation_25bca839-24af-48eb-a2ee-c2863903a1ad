"use client"

import { Card } from "@/components/ui/card"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"

interface TrendChartProps {
  data: any
}

export function TrendChart({ data }: TrendChartProps) {
  const trendData = data.trendData || []

  if (trendData.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">舆情趋势分析</h3>
        <div className="text-center text-muted-foreground py-8">
          暂无趋势数据
        </div>
      </Card>
    )
  }

  // 格式化日期显示
  const formattedData = trendData.map((item: any) => ({
    ...item,
    date: new Date(item.date).toLocaleDateString('zh-CN', { 
      month: 'short', 
      day: 'numeric' 
    })
  }))

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">舆情趋势分析（最近7天）</h3>
      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={formattedData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="positive" 
            stroke="#10b981" 
            name="正面"
            strokeWidth={2}
          />
          <Line 
            type="monotone" 
            dataKey="negative" 
            stroke="#ef4444" 
            name="负面"
            strokeWidth={2}
          />
          <Line 
            type="monotone" 
            dataKey="neutral" 
            stroke="#6b7280" 
            name="中性"
            strokeWidth={2}
          />
        </LineChart>
      </ResponsiveContainer>
      
      <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded-full" />
          <span>正面舆情</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full" />
          <span>负面舆情</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-gray-500 rounded-full" />
          <span>中性舆情</span>
        </div>
      </div>
    </Card>
  )
}