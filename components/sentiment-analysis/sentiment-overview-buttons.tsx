"use client"

import { TrendingUp } from "lucide-react"

interface SentimentOverviewButtonsProps {
  positiveNotes: number
  negativeNotes: number
  neutralNotes: number
  onClick: (sentiment: 'positive' | 'negative' | 'neutral', label: string) => void
}

export function SentimentOverviewButtons({
  positiveNotes,
  negativeNotes,
  neutralNotes,
  onClick
}: SentimentOverviewButtonsProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-2">
        <TrendingUp className="h-4 w-4" />
        <span>点击查看各情绪类别的笔记详情</span>
      </div>
      <div className="grid grid-cols-3 gap-2">
        <button
          onClick={() => onClick('positive', '正面笔记')}
          className="group relative overflow-hidden p-4 rounded-lg border bg-gradient-to-br from-green-50 to-green-100/50 hover:from-green-100 hover:to-green-200/50 transition-all duration-200 cursor-pointer"
        >
          <div className="relative z-10">
            <div className="text-green-700 font-medium mb-1">正面情绪</div>
            <div className="text-2xl font-bold text-green-800">{positiveNotes}</div>
            <div className="text-xs text-green-600 mt-1">篇笔记</div>
          </div>
          <div className="absolute inset-0 bg-green-400/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-200" />
        </button>
        
        <button
          onClick={() => onClick('negative', '负面笔记')}
          className="group relative overflow-hidden p-4 rounded-lg border bg-gradient-to-br from-red-50 to-red-100/50 hover:from-red-100 hover:to-red-200/50 transition-all duration-200 cursor-pointer"
        >
          <div className="relative z-10">
            <div className="text-red-700 font-medium mb-1">负面情绪</div>
            <div className="text-2xl font-bold text-red-800">{negativeNotes}</div>
            <div className="text-xs text-red-600 mt-1">篇笔记</div>
          </div>
          <div className="absolute inset-0 bg-red-400/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-200" />
        </button>
        
        <button
          onClick={() => onClick('neutral', '中性笔记')}
          className="group relative overflow-hidden p-4 rounded-lg border bg-gradient-to-br from-gray-50 to-gray-100/50 hover:from-gray-100 hover:to-gray-200/50 transition-all duration-200 cursor-pointer"
        >
          <div className="relative z-10">
            <div className="text-gray-700 font-medium mb-1">中性情绪</div>
            <div className="text-2xl font-bold text-gray-800">{neutralNotes}</div>
            <div className="text-xs text-gray-600 mt-1">篇笔记</div>
          </div>
          <div className="absolute inset-0 bg-gray-400/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-200" />
        </button>
      </div>
    </div>
  )
}