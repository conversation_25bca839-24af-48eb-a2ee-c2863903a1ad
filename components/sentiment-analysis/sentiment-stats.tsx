import { Card } from "@/components/ui/card"
import { ThumbsUp, MessageSquare, Bookmark, FileText } from "lucide-react"

interface SentimentStatsProps {
  data: any
}

export function SentimentStats({ data }: SentimentStatsProps) {
  const stats = [
    {
      title: "分析笔记数",
      value: data.totalNotes || 0,
      icon: FileText,
      color: "text-blue-600"
    },
    {
      title: "总点赞数",
      value: data.totalLikes || 0,
      icon: ThumbsUp,
      color: "text-red-600"
    },
    {
      title: "总评论数",
      value: data.totalComments || 0,
      icon: MessageSquare,
      color: "text-green-600"
    },
    {
      title: "总收藏数",
      value: data.totalCollects || 0,
      icon: Bookmark,
      color: "text-purple-600"
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">{stat.title}</p>
              <p className="text-2xl font-bold mt-1">
                {stat.value.toLocaleString()}
              </p>
            </div>
            <stat.icon className={`h-8 w-8 ${stat.color}`} />
          </div>
        </Card>
      ))}
    </div>
  )
}