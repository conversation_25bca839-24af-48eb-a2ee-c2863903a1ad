{"permissions": {"allow": ["Bash(grep:*)", "Bash(npm run lint)", "<PERSON><PERSON>(docker compose:*)", "mcp__ide__getDiagnostics", "Bash(npx tsc:*)", "<PERSON>sh(node test-email.js)", "Bash(npx tsx:*)", "Bash(rm:*)", "Bash(npm run dev:*)", "Bash(npm run db:push:*)", "Bash(npx prisma migrate dev:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run build:*)", "Bash(docker system prune:*)", "Bash(npm run lint:*)", "Bash(npm run tsx:*)", "Bash(npx next lint:*)", "Bash(ls:*)", "Bash(npm run:*)", "Bash(npm install:*)", "Bash(find:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx prisma db push:*)", "Bash(npx prisma db pull:*)", "Bash(npx prisma generate:*)", "<PERSON><PERSON>(true)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rg:*)", "Bash(^C)", "Bash(python test_font.py:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(mysql:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx prisma:*)", "Bash(node:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python test:*)", "mcp__ide__executeCode", "mcp__sequential-thinking__sequentialthinking", "Bash(cp:*)"], "deny": []}}