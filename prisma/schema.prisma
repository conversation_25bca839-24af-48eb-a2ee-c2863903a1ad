generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("MYSQL_DATABASE_URL")
}

model NoteType {
  id        Int      @id @default(autoincrement())
  name      String   @unique @db.VarChar(50)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@map("note_types")
}

model NoteStatus {
  id        Int      @id @default(autoincrement())
  name      String   @unique @db.VarChar(50)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@map("note_statuses")
}

model Note {
  id             String    @id @default(uuid()) @db.Char(36)
  title          String    @db.VarChar(255)
  content        String?   @db.Text
  coverImage     String?   @map("cover_image") @db.Var<PERSON>har(255)
  url            String?   @db.Var<PERSON>har(255)
  likes          Int       @default(0)
  commentsCount  Int       @default(0) @map("comments")
  saves          Int       @default(0)
  potentialScore Int       @default(0) @map("potential_score")
  typeId         Int?      @map("type_id")
  statusId       Int?      @map("status_id")
  publishedAt    DateTime? @map("published_at") @db.Timestamp(0)
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt      DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  userId         String?   @map("user_id") @db.Char(36)
  noteId         String    @unique @map("note_id") @db.VarChar(255)
  contentImages  Json      @map("content_images")

  @@index([noteId], map: "idx_notes_note_id")
  @@index([publishedAt], map: "idx_notes_published_at")
  @@index([userId], map: "idx_notes_user_id")
  @@map("notes")
}

model UserNote {
  id             Int      @id @default(autoincrement())
  userId         String   @map("user_id") @db.Char(36)
  noteId         String   @map("note_id") @db.VarChar(255)
  createdAt      DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  collectBatchId String?  @map("collect_batch_id") @db.Text
  searchKeyword  String?  @map("search_keyword") @db.Text
  userNoteType   Int?     @default(1) @map("user_note_type")

  @@map("user_notes")
}

model Comment {
  id          String    @id @default(uuid()) @db.Char(36)
  userName    String    @map("user_name") @db.Text
  userUid     String?   @map("user_uid") @db.Text
  content     String    @db.Text
  commentDate DateTime? @map("comment_date") @db.DateTime(0)
  likesCount  Int       @default(0) @map("likes_count")
  subComments Json      @map("sub_comments")
  collectedAt DateTime  @map("collected_at") @db.DateTime(0)
  updatedAt   DateTime  @map("updated_at") @db.DateTime(0)
  noteId      String    @map("note_id") @db.VarChar(255)
  commentId   String    @map("comment_id") @db.VarChar(255)

  @@unique([commentId, noteId])
  @@map("comments")
}

model ApiKey {
  id          String    @id @default(uuid()) @db.Char(36)
  userId      String    @map("user_id") @db.Char(36)
  keyValue    String    @map("key_value") @db.Text
  name        String    @db.Text
  description String?   @db.Text
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  expiresAt   DateTime? @map("expires_at") @db.Timestamp(0)
  lastUsedAt  DateTime? @map("last_used_at") @db.Timestamp(0)

  @@map("api_keys")
}

model ApiKeyHistory {
  id               String   @id @default(uuid()) @db.Char(36)
  userId           String   @map("user_id") @db.Char(36)
  keyValue         String   @map("key_value") @db.Text
  name             String   @db.Text
  description      String?  @db.Text
  createdAt        DateTime @map("created_at") @db.Timestamp(0)
  revokedAt        DateTime @default(now()) @map("revoked_at") @db.Timestamp(0)
  revocationReason String?  @map("revocation_reason") @db.Text

  @@map("api_key_history")
}

model RewriteQueue {
  id               String   @id @default(uuid()) @db.Char(36)
  userId           String   @map("user_id") @db.Char(36)
  noteId           String   @map("note_id") @db.VarChar(255)
  status           String   @map("status") @db.VarChar(50)
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  resultId         String?  @map("result_id") @db.Char(36)
  coverImageTaskId String?  @map("cover_image_task_id") @db.Text
  coverImageUrls   Json?    @map("cover_image_urls")
  xhsNoteId        String?  @map("xhs_note_id") @db.Text

  @@map("rewrite_queue")
}

model RewrittenNoteStatus {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(50)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@map("rewritten_note_statuses")
}

model RewrittenNote {
  id                 String    @id @default(uuid()) @db.Char(36)
  originalNoteId     String?   @map("original_note_id") @db.Char(36)
  title              String    @db.VarChar(255)
  content            String?   @db.Text
  coverImage         String?   @map("cover_image") @db.VarChar(255)
  contentImages      Json?     @map("content_images")
  tags               Json?
  statusId           Int?      @default(1) @map("status_id")
  platformId         Int?      @map("platform_id")
  accountId          Int?      @map("account_id")
  publishUrl         String?   @map("publish_url") @db.VarChar(255)
  publishResult      Json?     @map("publish_result")
  publishedAt        DateTime? @map("published_at") @db.Timestamp(0)
  createdAt          DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt          DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  userId             String?   @map("user_id") @db.Char(36)
  noteId             String    @unique @map("note_id") @db.VarChar(255)
  rewritten_queue_id String?   @db.VarChar(255)
  viralNoteTaskId    String?   @map("viral_note_task_id") @db.VarChar(255)

  @@map("rewritten_notes")
}

model UserRewrittenNote {
  id         Int       @id @default(autoincrement())
  userId     String    @map("user_id") @db.Char(36)
  noteId     String    @map("note_id") @db.VarChar(255)
  createdAt  DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updated_at DateTime? @db.DateTime(0)

  @@map("user_rewritten_notes")
}

model UserTier {
  id                Int      @id @default(autoincrement())
  name              String   @db.VarChar(50)
  description       String?  @db.Text
  monthlyAiQuota    Int      @map("monthly_ai_quota")
  price             Decimal  @default(0.00) @db.Decimal(10, 2)
  isDefault         Boolean  @default(false) @map("is_default")
  createdAt         DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  monthlyPoints     Int      @default(0) @map("monthly_points")
  pointsDescription String?  @map("points_description") @db.Text
  yearlyPoints      Int      @default(0) @map("yearly_points")

  @@map("user_tiers")
}

model UserTierAssignment {
  id        Int       @id @default(autoincrement())
  userId    String    @map("user_id") @db.Char(36)
  tierId    Int       @map("tier_id")
  startDate DateTime  @default(now()) @map("start_date") @db.Timestamp(0)
  endDate   DateTime? @map("end_date") @db.Timestamp(0)
  isActive  Boolean   @default(true) @map("is_active")
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@map("user_tier_assignments")
}

model AiUsageRecord {
  id        Int      @id @default(autoincrement())
  userId    String   @map("user_id") @db.Char(36)
  usageType String   @map("usage_type") @db.VarChar(50)
  metadata  Json?
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@map("ai_usage_records")
}

model MonthlyAiUsageStat {
  id         Int    @id @default(autoincrement())
  userId     String @map("user_id") @db.Char(36)
  year       Int
  month      Int
  usageType  String @map("usage_type") @db.VarChar(50)
  totalUsage Int    @map("total_usage")

  @@unique([userId, year, month, usageType])
  @@map("monthly_ai_usage_stats")
}

model AiQuotaAddition {
  id        Int       @id @default(autoincrement())
  userId    String    @map("user_id") @db.Char(36)
  amount    Int
  reason    String    @db.Text
  createdBy String    @map("created_by") @db.Char(36)
  expiresAt DateTime? @map("expires_at") @db.Timestamp(0)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamp(0)

  @@map("ai_quota_additions")
}

model AiSetting {
  id                     String  @id @default(uuid()) @db.Char(36)
  userId                 String  @unique @map("user_id") @db.Char(36)
  aiModel                String  @map("ai_model") @db.VarChar(100)
  encryptedApiKey        String? @map("encrypted_api_key") @db.Text
  defaultPrompt          String  @map("default_prompt") @db.Text
  writingStyle           String  @map("writing_style") @db.VarChar(50)
  emojiLevel             String  @map("emoji_level") @db.VarChar(50)
  autoTags               Boolean @default(true) @map("auto_tags")
  autoTitle              Boolean @default(true) @map("auto_title")
  titlePrompt            String  @map("title_prompt") @db.Text
  imageModel             String  @map("image_model") @db.VarChar(100)
  imageStyle             String  @map("image_style") @db.VarChar(100)
  imagePrompt            String  @map("image_prompt") @db.Text
  coverImageGenerateType Int     @default(0) @map("cover_image_generate_type")
  coverImageCount        Int     @default(1) @map("cover_image_count")

  @@map("ai_settings")
}

model PromptTemplate {
  id            String   @id @default(uuid()) @db.Char(36)
  userId        String   @map("user_id") @db.Char(36)
  name          String   @db.VarChar(255)
  titlePrompt   String?  @map("title_prompt") @db.Text
  contentPrompt String   @map("content_prompt") @db.Text
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([userId], map: "idx_prompt_templates_user_id")
  @@index([userId, createdAt], map: "idx_prompt_templates_user_id_created_at")
  @@map("prompt_templates")
}

model UserProfile {
  id        Int      @id @default(autoincrement())
  userId    String   @unique(map: "idx_user_profiles_user_id") @map("user_id") @db.Char(36)
  username  String?  @db.VarChar(30)
  bio       String?  @db.Text
  avatarUrl String?  @map("avatar_url") @db.Text
  website   String?  @db.VarChar(255)
  location  String?  @db.VarChar(100)
  company   String?  @db.VarChar(100)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@map("user_profiles")
}

model PublishPlatform {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(50)
  icon      String?  @db.VarChar(255)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@map("publish_platforms")
}

model PublishAccount {
  id         Int      @id @default(autoincrement())
  platformId Int?     @map("platform_id")
  name       String   @db.VarChar(100)
  username   String?  @db.VarChar(100)
  avatar     String?  @db.VarChar(255)
  status     String   @default("active") @db.VarChar(50)
  createdAt  DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  userId     String?  @map("user_id") @db.Char(36)

  @@map("publish_accounts")
}

model UserComment {
  id           String   @id @default(uuid()) @db.Char(36)
  userId       String   @map("user_id") @db.Char(36)
  createdAt    DateTime @default(now()) @map("created_at") @db.DateTime(0)
  noteId       String   @map("note_id") @db.Char(36)
  scrapeStatus Int?     @map("scrape_status")

  @@unique([userId, noteId])
  @@map("user_comments")
}

model UserBatch {
  id             String    @id @default(uuid()) @db.Char(36)
  userId         String    @map("user_id") @db.Char(36)
  name           String    @db.VarChar(255)
  keyword        String    @db.VarChar(255)
  likesThreshold Int       @default(0) @map("likes_threshold")
  startTime      DateTime? @map("start_time") @db.Timestamp(0)
  endTime        DateTime? @map("end_time") @db.Timestamp(0)
  status         String    @default("active") @db.VarChar(50)
  notesCount     Int       @default(0) @map("notes_count")
  commentedCount Int       @default(0) @map("commented_count")
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt      DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([createdAt], map: "idx_user_batches_created_at")
  @@index([userId], map: "idx_user_batches_user_id")
  @@index([userId, status], map: "idx_user_batches_user_id_status")
  @@map("user_batches")
}

model BatchNote {
  id        Int      @id @default(autoincrement())
  batchId   String   @map("batch_id") @db.Char(36)
  xhsNoteId String   @map("xhs_note_id") @db.VarChar(255)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@unique([batchId, xhsNoteId])
  @@index([batchId], map: "idx_batch_notes_batch_id")
  @@index([batchId, createdAt], map: "idx_batch_notes_batch_id_created_at")
  @@index([xhsNoteId], map: "idx_batch_notes_xhs_note_id")
  @@map("batch_notes")
}

model UserNoteInteraction {
  id             String    @id @default(uuid()) @db.Char(36)
  userId         String    @map("user_id") @db.Char(36)
  xhsNoteId      String    @map("xhs_note_id") @db.VarChar(255)
  batchId        String    @map("batch_id") @db.Char(36)
  judgmentResult String?   @map("judgment_result") @db.VarChar(50)
  isCommented    Boolean   @default(false) @map("is_commented")
  commentContent String?   @map("comment_content") @db.Text
  commentDate    DateTime? @map("comment_date") @db.Timestamp(0)
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt      DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@unique([userId, xhsNoteId, batchId])
  @@index([batchId], map: "idx_user_note_interactions_batch_id")
  @@index([batchId, isCommented], map: "idx_user_note_interactions_batch_id_is_commented")
  @@index([userId], map: "idx_user_note_interactions_user_id")
  @@index([userId, batchId], map: "idx_user_note_interactions_user_id_batch_id")
  @@index([xhsNoteId], map: "idx_user_note_interactions_xhs_note_id")
  @@map("user_note_interactions")
}

model UserCollectBatch {
  id               String   @id @default(uuid()) @db.Char(36)
  userId           String   @map("user_id") @db.Char(36)
  collectBatchName String   @map("collect_batch_name") @db.VarChar(255)
  searchKeyword    String   @map("search_keyword") @db.VarChar(255)
  metaData         String?  @map("meta_data") @db.Text
  collectTime      DateTime @map("collect_time") @db.Timestamp(0)
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@map("user_collect_batch")
}

model LogCozeInvoke {
  id          String   @id @default(uuid()) @db.Char(36)
  userId      String   @map("user_id") @db.Char(36)
  invokeParam String   @map("invoke_param") @db.Text
  respInfo    String   @map("resp_info") @db.Text
  extend      String?  @map("extend") @db.VarChar(255)
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@map("log_coze_invoke")
}

model User {
  id                  String    @id @default(uuid()) @db.Char(36)
  name                String?   @db.VarChar(255)
  email               String?   @unique @db.VarChar(255)
  emailVerified       Boolean   @default(false) @map("emailVerified")
  image               String?   @db.VarChar(500)
  role                String    @default("user") @db.VarChar(50)
  isAdmin             Boolean   @default(false) @map("isAdmin")
  createdAt           DateTime  @default(now()) @map("createdAt") @db.Timestamp(0)
  updatedAt           DateTime  @default(now()) @updatedAt @map("updatedAt") @db.Timestamp(0)
  phoneNumber         String?   @unique @map("phoneNumber") @db.VarChar(20)
  phoneNumberVerified Boolean   @default(false) @map("phoneNumberVerified")
  deletedAt           DateTime? @map("deletedAt") @db.Timestamp(0)

  @@map("user")
}

model Account {
  id                    String    @id @default(uuid()) @db.Char(36)
  accountId             String    @map("accountId") @db.VarChar(255)
  providerId            String    @map("providerId") @db.VarChar(255)
  userId                String    @map("userId") @db.Char(36)
  accessToken           String?   @map("accessToken") @db.Text
  refreshToken          String?   @map("refreshToken") @db.Text
  idToken               String?   @map("idToken") @db.Text
  accessTokenExpiresAt  DateTime? @map("accessTokenExpiresAt") @db.Timestamp(0)
  refreshTokenExpiresAt DateTime? @map("refreshTokenExpiresAt") @db.Timestamp(0)
  scope                 String?   @db.VarChar(500)
  password              String?   @db.VarChar(255)
  createdAt             DateTime  @default(now()) @map("createdAt") @db.Timestamp(0)
  updatedAt             DateTime  @default(now()) @updatedAt @map("updatedAt") @db.Timestamp(0)

  @@unique([providerId, accountId])
  @@map("account")
}

model Session {
  id        String   @id @default(uuid()) @db.Char(36)
  expiresAt DateTime @map("expiresAt") @db.Timestamp(0)
  token     String   @unique @db.VarChar(255)
  userId    String   @map("userId") @db.Char(36)
  ipAddress String?  @map("ipAddress") @db.VarChar(45)
  userAgent String?  @map("userAgent") @db.Text
  createdAt DateTime @default(now()) @map("createdAt") @db.Timestamp(0)
  updatedAt DateTime @default(now()) @updatedAt @map("updatedAt") @db.Timestamp(0)

  @@map("session")
}

model Verification {
  id         String   @id @default(uuid()) @db.Char(36)
  identifier String   @db.VarChar(255)
  value      String   @db.VarChar(255)
  expiresAt  DateTime @map("expiresAt") @db.Timestamp(0)
  createdAt  DateTime @default(now()) @map("createdAt") @db.Timestamp(0)
  updatedAt  DateTime @default(now()) @updatedAt @map("updatedAt") @db.Timestamp(0)

  @@unique([identifier, value])
  @@map("verification")
}

model AgentUserAssignment {
  id        String   @id @default(uuid()) @db.Char(36)
  agentId   String   @map("agent_id") @db.Char(36)
  userId    String   @map("user_id") @db.Char(36)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@unique([agentId, userId])
  @@map("agent_user_assignments")
}

model UserInvitation {
  id          String    @id @default(uuid()) @db.Char(36)
  inviterId   String    @map("inviter_id") @db.Char(36)
  inviterRole String    @map("inviter_role") @db.VarChar(20)
  email       String?   @db.VarChar(255)
  phoneNumber String?   @map("phone_number") @db.VarChar(20)
  inviteCode  String    @unique @map("invite_code") @db.VarChar(32)
  status      String    @default("pending") @db.VarChar(20)
  expiresAt   DateTime  @map("expires_at") @db.Timestamp(0)
  acceptedAt  DateTime? @map("accepted_at") @db.Timestamp(0)
  acceptedBy  String?   @map("accepted_by") @db.Char(36)
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  inviteType  String    @default("temporary") @map("invite_type") @db.VarChar(20)
  maxUses     Int?      @map("max_uses")
  usedCount   Int       @default(0) @map("used_count")
  isActive    Boolean   @default(true) @map("is_active")
  note        String?   @map("note") @db.VarChar(255)

  @@map("user_invitations")
}

model EmailChangeRequest {
  id          String    @id @default(uuid()) @db.Char(36)
  userId      String    @map("user_id") @db.Char(36)
  oldEmail    String?   @map("old_email") @db.VarChar(255)
  newEmail    String    @map("new_email") @db.VarChar(255)
  token       String    @unique(map: "email_change_requests_token_unique") @db.VarChar(255)
  status      String    @default("pending") @db.VarChar(20)
  expiresAt   DateTime  @map("expires_at") @db.Timestamp(0)
  confirmedAt DateTime? @map("confirmed_at") @db.Timestamp(0)
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@map("email_change_requests")
}

model PhoneChangeRequest {
  id          String    @id @default(uuid()) @db.Char(36)
  userId      String    @map("user_id") @db.Char(36)
  oldPhone    String?   @map("old_phone") @db.VarChar(20)
  newPhone    String    @map("new_phone") @db.VarChar(20)
  code        String    @db.VarChar(10)
  status      String    @default("pending") @db.VarChar(20)
  attempts    Int       @default(0)
  maxAttempts Int       @default(3) @map("max_attempts")
  expiresAt   DateTime  @map("expires_at") @db.Timestamp(0)
  confirmedAt DateTime? @map("confirmed_at") @db.Timestamp(0)
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@map("phone_change_requests")
}

model AiChatSession {
  id          String   @id @default(uuid()) @db.Char(36)
  userId      String   @map("user_id") @db.Char(36)
  title       String?  @db.VarChar(255)
  lastMessage String?  @map("last_message") @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([userId])
  @@index([userId, isActive])
  @@map("ai_chat_sessions")
}

model AiChatMessage {
  id        String   @id @default(uuid()) @db.Char(36)
  sessionId String   @map("session_id") @db.Char(36)
  userId    String   @map("user_id") @db.Char(36)
  role      String   @db.VarChar(20)
  content   String   @db.Text
  metadata  Json?
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@index([sessionId])
  @@index([userId])
  @@index([sessionId, createdAt])
  @@map("ai_chat_messages")
}

model ViralNoteTask {
  id           String   @id @default(uuid()) @db.Char(36)
  userId       String   @map("user_id") @db.Char(36)
  topic        String   @db.Text
  writingStyle String   @map("writing_style") @db.VarChar(50)
  imageStyle   String   @map("image_style") @db.VarChar(50)
  imageCount   Int      @map("image_count")
  accountName  String   @map("account_name") @db.VarChar(255)
  status       String   @default("pending") @db.VarChar(50)
  result       String?  @db.Text
  errorMessage String?  @map("error_message") @db.Text
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([userId])
  @@index([status])
  @@index([userId, status])
  @@index([createdAt])
  @@map("viral_note_tasks")
}

model userinvitation {
  id        String                @id
  email     String
  inviterId String
  token     String                @unique
  status    userinvitation_status @default(pending)
  createdAt DateTime              @default(now())
  updatedAt DateTime              @default(now())
  expiresAt DateTime

  @@index([email])
  @@index([inviterId])
  @@index([status])
}

model AiModelConfig {
  id          Int      @id @default(autoincrement())
  modelName   String   @unique @map("model_name") @db.VarChar(100)
  modelType   String   @map("model_type") @db.VarChar(50)
  pointsCost  Int      @default(10) @map("points_cost")
  description String?  @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([modelType], map: "idx_model_type")
  @@index([isActive], map: "idx_is_active")
  @@map("ai_model_configs")
}

model UserPointsWallet {
  id          Int      @id @default(autoincrement())
  userId      String   @unique @map("user_id") @db.Char(36)
  balance     Int      @default(0)
  totalEarned Int      @default(0) @map("total_earned")
  totalSpent  Int      @default(0) @map("total_spent")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([balance], map: "idx_balance")
  @@map("user_points_wallets")
}

model PointsTransaction {
  id           String    @id @default(uuid()) @db.Char(36)
  userId       String    @map("user_id") @db.Char(36)
  type         String    @db.VarChar(50)
  amount       Int
  balanceAfter Int       @map("balance_after")
  description  String?   @db.Text
  metadata     Json?
  relatedId    String?   @map("related_id") @db.Char(36)
  relatedType  String?   @map("related_type") @db.VarChar(50)
  expiresAt    DateTime? @map("expires_at") @db.Timestamp(0)
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamp(0)

  @@index([userId], map: "idx_user_id")
  @@index([type], map: "idx_type")
  @@index([createdAt], map: "idx_created_at")
  @@index([relatedId, relatedType], map: "idx_related")
  @@index([expiresAt], map: "idx_expires_at")
  @@map("points_transactions")
}

model PointsGrant {
  id                 String    @id @default(uuid()) @db.Char(36)
  userId             String    @map("user_id") @db.Char(36)
  grantType          String    @map("grant_type") @db.VarChar(50)
  amount             Int
  reason             String?   @db.Text
  grantedBy          String?   @map("granted_by") @db.Char(36)
  subscriptionTierId Int?      @map("subscription_tier_id")
  subscriptionPeriod String?   @map("subscription_period") @db.VarChar(20)
  validUntil         DateTime? @map("valid_until") @db.Timestamp(0)
  transactionId      String?   @map("transaction_id") @db.Char(36)
  createdAt          DateTime  @default(now()) @map("created_at") @db.Timestamp(0)

  @@index([userId], map: "idx_user_id")
  @@index([grantType], map: "idx_grant_type")
  @@index([grantedBy], map: "idx_granted_by")
  @@index([createdAt], map: "idx_created_at")
  @@index([subscriptionTierId, subscriptionPeriod], map: "idx_subscription")
  @@map("points_grants")
}

model AiPointsConsumption {
  id            String   @id @default(uuid()) @db.Char(36)
  userId        String   @map("user_id") @db.Char(36)
  modelName     String   @map("model_name") @db.VarChar(100)
  modelType     String   @map("model_type") @db.VarChar(50)
  pointsCost    Int      @map("points_cost")
  usageType     String   @map("usage_type") @db.VarChar(50)
  metadata      Json?
  transactionId String?  @map("transaction_id") @db.Char(36)
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@index([userId], map: "idx_user_id")
  @@index([modelName], map: "idx_model_name")
  @@index([usageType], map: "idx_usage_type")
  @@index([createdAt], map: "idx_created_at")
  @@index([transactionId], map: "idx_transaction_id")
  @@map("ai_points_consumptions")
}

model PublishPlan {
  id                  String   @id @default(uuid()) @db.Char(36)
  userId              String   @map("user_id") @db.Char(36)
  name                String   @db.VarChar(255)
  description         String?  @db.Text
  publishMode         String   @default("qr_code") @map("publish_mode") @db.VarChar(50)
  status              String   @default("draft") @db.VarChar(50)
  totalNotesCount     Int      @default(0) @map("total_notes_count")
  publishedNotesCount Int      @default(0) @map("published_notes_count")
  qrCodeToken         String?  @unique @map("qr_code_token") @db.VarChar(64)
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt           DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([userId], map: "idx_publish_plans_user_id")
  @@index([status], map: "idx_publish_plans_status")
  @@index([qrCodeToken], map: "idx_publish_plans_qr_code_token")
  @@map("publish_plans")
}

model PublishPlanNote {
  id              Int       @id @default(autoincrement())
  planId          String    @map("plan_id") @db.Char(36)
  rewrittenNoteId String    @map("rewritten_note_id") @db.Char(36)
  status          String    @default("pending") @db.VarChar(50)
  publishedAt     DateTime? @map("published_at") @db.Timestamp(0)
  createdAt       DateTime  @default(now()) @map("created_at") @db.Timestamp(0)

  @@unique([planId, rewrittenNoteId], map: "unique_plan_note")
  @@index([planId], map: "idx_publish_plan_notes_plan_id")
  @@index([rewrittenNoteId], map: "idx_publish_plan_notes_rewritten_note_id")
  @@index([status], map: "idx_publish_plan_notes_status")
  @@map("publish_plan_notes")
}

enum userinvitation_status {
  pending
  accepted
  expired
}

model MaterialFolder {
  id          String   @id @default(uuid()) @db.Char(36)
  userId      String   @map("user_id") @db.Char(36)
  name        String   @db.VarChar(255)
  parentId    String?  @map("parent_id") @db.Char(36)
  path        String   @db.Text
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)

  @@index([userId], map: "idx_material_folders_user_id")
  @@index([parentId], map: "idx_material_folders_parent_id")
  @@index([userId, parentId], map: "idx_material_folders_user_parent")
  @@map("material_folders")
}

model MaterialTag {
  id        String   @id @default(uuid()) @db.Char(36)
  userId    String   @map("user_id") @db.Char(36)
  name      String   @db.VarChar(100)
  color     String?  @db.VarChar(20)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@unique([userId, name], map: "unique_user_tag")
  @@index([userId], map: "idx_material_tags_user_id")
  @@map("material_tags")
}

model Material {
  id           String    @id @default(uuid()) @db.Char(36)
  userId       String    @map("user_id") @db.Char(36)
  folderId     String?   @map("folder_id") @db.Char(36)
  fileName     String    @map("file_name") @db.VarChar(255)
  displayName  String    @map("display_name") @db.VarChar(255)
  fileType     String    @map("file_type") @db.VarChar(50)
  fileSize     Int       @map("file_size")
  fileUrl      String    @map("file_url") @db.Text
  thumbnailUrl String?   @map("thumbnail_url") @db.Text
  metadata     Json?
  description  String?   @db.Text
  tags         Json?
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  deletedAt    DateTime? @map("deleted_at") @db.Timestamp(0)

  @@index([userId], map: "idx_materials_user_id")
  @@index([folderId], map: "idx_materials_folder_id")
  @@index([fileType], map: "idx_materials_file_type")
  @@index([createdAt], map: "idx_materials_created_at")
  @@index([userId, folderId], map: "idx_materials_user_folder")
  @@index([userId, deletedAt], map: "idx_materials_user_deleted")
  @@map("materials")
}

model SentimentAnalysisCache {
  id                    String    @id @default(uuid()) @db.Char(36)
  userId                String    @map("user_id") @db.Char(36)
  keyword               String    @db.VarChar(255)
  totalNotes            Int       @default(0) @map("total_notes")
  totalLikes            Int       @default(0) @map("total_likes")
  totalComments         Int       @default(0) @map("total_comments")
  totalCollects         Int       @default(0) @map("total_collects")
  positiveNotes         Int       @default(0) @map("positive_notes")
  negativeNotes         Int       @default(0) @map("negative_notes")
  neutralNotes          Int       @default(0) @map("neutral_notes")
  positiveComments      Int       @default(0) @map("positive_comments")
  negativeComments      Int       @default(0) @map("negative_comments")
  neutralComments       Int       @default(0) @map("neutral_comments")
  topNotesData          Json?     @map("top_notes_data")
  keywordsCloud         Json?     @map("keywords_cloud")
  sentimentDistribution Json?     @map("sentiment_distribution")
  trendData             Json?     @map("trend_data")
  aiSummaryReport       String?   @map("ai_summary_report") @db.Text
  noteIds               Json?     @map("note_ids")
  metadata              Json?
  status                String    @default("pending") @db.VarChar(50)
  createdAt             DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt             DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  expiresAt             DateTime? @map("expires_at") @db.Timestamp(0)

  @@unique([userId, keyword], map: "unique_user_keyword")
  @@index([userId], map: "idx_sentiment_analysis_user_id")
  @@index([keyword], map: "idx_sentiment_analysis_keyword")
  @@index([status], map: "idx_sentiment_analysis_status")
  @@index([createdAt], map: "idx_sentiment_analysis_created_at")
  @@map("sentiment_analysis_cache")
}

model SentimentAnalysisDetail {
  id         String   @id @default(uuid()) @db.Char(36)
  cacheId    String   @map("cache_id") @db.Char(36)
  noteId     String   @map("note_id") @db.VarChar(255)
  sentiment  String   @db.VarChar(20)
  confidence Float    @default(0.0)
  keywords   Json?
  createdAt  DateTime @default(now()) @map("created_at") @db.Timestamp(0)

  @@index([cacheId], map: "idx_sentiment_detail_cache_id")
  @@index([noteId], map: "idx_sentiment_detail_note_id")
  @@map("sentiment_analysis_details")
}

model BatchGenerationSession {
  id              String   @id @default(uuid()) @db.Char(36)
  userId          String   @map("user_id") @db.Char(36)
  sessionName     String   @map("session_name") @db.VarChar(255)
  totalGenerated  Int      @default(0) @map("total_generated")
  metadata        Json?
  createdAt       DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  
  @@index([userId], map: "idx_batch_generation_sessions_user_id")
  @@index([createdAt], map: "idx_batch_generation_sessions_created_at")
  @@map("batch_generation_sessions")
}

model BatchGeneratedNote {
  id                String    @id @default(uuid()) @db.Char(36)
  userId            String    @map("user_id") @db.Char(36)
  title             String    @db.VarChar(255)
  content           String    @db.Text
  coverImage        String    @map("cover_image") @db.LongText
  contentImages     Json      @map("content_images")
  
  batchSessionId    String    @map("batch_session_id") @db.Char(36)
  batchIndex        Int       @map("batch_index")
  keywords          Json
  templateSettings  Json      @map("template_settings")
  textOverlayConfig Json      @map("text_overlay_config")
  generationConfig  Json      @map("generation_config")
  
  isAddedToPublish  Boolean   @default(false) @map("is_added_to_publish")
  publishedNoteId   String?   @map("published_note_id") @db.Char(36)
  
  createdAt         DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  
  @@index([userId], map: "idx_batch_generated_notes_user_id")
  @@index([batchSessionId], map: "idx_batch_generated_notes_batch_session_id")
  @@index([isAddedToPublish], map: "idx_batch_generated_notes_is_added_to_publish")
  @@index([createdAt], map: "idx_batch_generated_notes_created_at")
  @@map("batch_generated_notes")
}
