import { prisma } from '../lib/database/mysql-client'

async function addSentimentAnalysisPointsConfig() {
  try {
    console.log('开始添加舆情分析积分配置...')
    
    // 定义需要添加的模型配置
    const newConfigs = [
      {
        modelName: 'sentiment_analysis',
        modelType: 'text_generation',
        pointsCost: 30,
        description: '舆情分析 - 关键词情感分析',
        isActive: true
      },
      {
        modelName: 'ai_report_generation',
        modelType: 'text_generation',
        pointsCost: 20,
        description: 'AI分析报告生成',
        isActive: true
      }
    ]

    // 添加配置
    for (const config of newConfigs) {
      const existing = await prisma.aiModelConfig.findUnique({
        where: { modelName: config.modelName }
      })

      if (existing) {
        console.log(`模型 ${config.modelName} 已存在，当前积分消耗: ${existing.pointsCost}`)
        
        // 如果积分消耗不同，更新它
        if (existing.pointsCost !== config.pointsCost) {
          await prisma.aiModelConfig.update({
            where: { modelName: config.modelName },
            data: { pointsCost: config.pointsCost }
          })
          console.log(`  更新积分消耗: ${existing.pointsCost} -> ${config.pointsCost}`)
        }
      } else {
        await prisma.aiModelConfig.create({
          data: config
        })
        console.log(`创建新模型配置: ${config.modelName} (${config.pointsCost}积分)`)
      }
    }

    console.log('\n舆情分析积分配置添加完成！')
    
    // 显示相关配置
    const sentimentConfigs = await prisma.aiModelConfig.findMany({
      where: {
        modelName: {
          in: ['sentiment_analysis', 'ai_report_generation']
        }
      }
    })
    
    console.log('\n当前舆情分析相关配置:')
    for (const config of sentimentConfigs) {
      console.log(`- ${config.modelName}: ${config.pointsCost}积分 (${config.description})`)
    }

  } catch (error) {
    console.error('添加配置时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
addSentimentAnalysisPointsConfig()