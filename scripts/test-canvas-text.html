<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas 文字合成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #ddd;
            display: block;
            margin: 20px 0;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a60d8;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-item {
            text-align: center;
        }
        .test-item h3 {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas 文字合成测试</h1>
        <p>测试各种文字样式在图片上的效果</p>
        
        <div>
            <input type="file" id="imageUpload" accept="image/*">
            <button onclick="runAllTests()">运行所有测试</button>
        </div>
        
        <div id="results" class="grid"></div>
    </div>

    <script>
        let testImage = null;
        
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = new Image();
                    img.onload = function() {
                        testImage = img;
                        console.log('图片加载成功:', img.width, 'x', img.height);
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
        
        function processImageWithText(img, overlay) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = img.width;
            canvas.height = img.height;
            
            // 1. 绘制原始图片
            ctx.drawImage(img, 0, 0);
            console.log('绘制了原始图片');
            
            // 2. 添加半透明遮罩
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            console.log('添加了遮罩层');
            
            // 3. 设置字体
            const fontSize = overlay.fontSize || 80;
            ctx.font = `bold ${fontSize}px "PingFang SC", "Microsoft YaHei", Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 4. 计算文字位置
            const x = canvas.width / 2;
            const y = canvas.height / 2;
            
            // 5. 绘制文字背景（如果需要）
            if (overlay.bgColor && overlay.bgColor !== 'transparent') {
                const metrics = ctx.measureText(overlay.text);
                const padding = 20;
                ctx.fillStyle = overlay.bgColor;
                ctx.fillRect(
                    x - metrics.width / 2 - padding,
                    y - fontSize / 2 - padding,
                    metrics.width + padding * 2,
                    fontSize + padding * 2
                );
            }
            
            // 6. 根据样式绘制文字
            console.log('开始绘制文字:', overlay.text, '样式:', overlay.textStyle);
            
            switch(overlay.textStyle) {
                case 'outline':
                    // 描边效果
                    ctx.strokeStyle = overlay.shadowColor || '#000000';
                    ctx.lineWidth = 8;
                    ctx.lineJoin = 'round';
                    ctx.strokeText(overlay.text, x, y);
                    ctx.fillStyle = overlay.color;
                    ctx.fillText(overlay.text, x, y);
                    break;
                    
                case 'gradient':
                    // 渐变效果
                    const gradient = ctx.createLinearGradient(
                        x - 200, y - 50,
                        x + 200, y + 50
                    );
                    gradient.addColorStop(0, overlay.color);
                    gradient.addColorStop(0.5, '#ff6b6b');
                    gradient.addColorStop(1, overlay.shadowColor || '#000000');
                    ctx.fillStyle = gradient;
                    ctx.fillText(overlay.text, x, y);
                    break;
                    
                case 'neon':
                    // 霓虹效果
                    ctx.shadowColor = overlay.color;
                    ctx.shadowBlur = 20;
                    ctx.fillStyle = overlay.color;
                    ctx.fillText(overlay.text, x, y);
                    ctx.shadowBlur = 40;
                    ctx.fillText(overlay.text, x, y);
                    ctx.shadowBlur = 0;
                    break;
                    
                default:
                    // 普通效果
                    ctx.shadowColor = overlay.shadowColor || '#000000';
                    ctx.shadowBlur = 10;
                    ctx.shadowOffsetX = 4;
                    ctx.shadowOffsetY = 4;
                    ctx.fillStyle = overlay.color;
                    ctx.fillText(overlay.text, x, y);
            }
            
            // 7. 绘制副标题
            if (overlay.subText) {
                ctx.font = `${fontSize * 0.4}px Arial, sans-serif`;
                ctx.fillStyle = overlay.color;
                ctx.shadowBlur = 0;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                
                const lines = overlay.subText.split('\n');
                lines.forEach((line, index) => {
                    const subY = y + fontSize + 30 + (index * fontSize * 0.5);
                    ctx.fillText(line, x, subY);
                });
            }
            
            // 8. 添加装饰元素
            if (overlay.includeDecorations) {
                // HOT角标
                ctx.save();
                ctx.translate(100, 100);
                ctx.rotate(-Math.PI / 4);
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(-60, -20, 120, 40);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('HOT', 0, 0);
                ctx.restore();
                
                // 装饰图标
                ctx.font = '30px Arial';
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.textAlign = 'left';
                ctx.fillText('✨', 50, 50);
                ctx.fillText('💫', canvas.width - 80, 50);
            }
            
            console.log('文字绘制完成');
            return canvas;
        }
        
        function runAllTests() {
            if (!testImage) {
                alert('请先上传一张图片');
                return;
            }
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            const testCases = [
                {
                    name: '描边效果',
                    overlay: {
                        text: '爆款笔记',
                        subText: '小红书封面\n一键生成',
                        textStyle: 'outline',
                        fontSize: 80,
                        color: '#ffffff',
                        shadowColor: '#000000',
                        bgColor: 'transparent',
                        includeDecorations: true
                    }
                },
                {
                    name: '渐变效果',
                    overlay: {
                        text: '美食探店',
                        subText: '必吃榜单\n人均50元',
                        textStyle: 'gradient',
                        fontSize: 96,
                        color: '#ffeb3b',
                        shadowColor: '#333333',
                        bgColor: 'rgba(0,0,0,0.4)',
                        includeDecorations: true
                    }
                },
                {
                    name: '霓虹效果',
                    overlay: {
                        text: '旅行攻略',
                        subText: '3天2夜\n完美行程',
                        textStyle: 'neon',
                        fontSize: 72,
                        color: '#00bcd4',
                        shadowColor: '#004a5c',
                        bgColor: 'transparent',
                        includeDecorations: false
                    }
                },
                {
                    name: '普通效果',
                    overlay: {
                        text: '生活分享',
                        subText: '日常vlog\n记录美好',
                        textStyle: 'normal',
                        fontSize: 88,
                        color: '#ffffff',
                        shadowColor: '#000000',
                        bgColor: 'rgba(0,0,0,0.5)',
                        includeDecorations: true
                    }
                }
            ];
            
            testCases.forEach(testCase => {
                try {
                    const canvas = processImageWithText(testImage, testCase.overlay);
                    
                    const testItem = document.createElement('div');
                    testItem.className = 'test-item';
                    
                    const title = document.createElement('h3');
                    title.textContent = testCase.name;
                    
                    testItem.appendChild(title);
                    testItem.appendChild(canvas);
                    
                    resultsDiv.appendChild(testItem);
                } catch (error) {
                    console.error('测试失败:', testCase.name, error);
                }
            });
        }
        
        // 创建一个默认图片用于测试
        window.onload = function() {
            const defaultCanvas = document.createElement('canvas');
            defaultCanvas.width = 800;
            defaultCanvas.height = 800;
            const ctx = defaultCanvas.getContext('2d');
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 800);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 800);
            
            // 转换为图片
            defaultCanvas.toBlob(function(blob) {
                const img = new Image();
                img.onload = function() {
                    testImage = img;
                    console.log('默认测试图片已加载');
                    runAllTests();
                };
                img.src = URL.createObjectURL(blob);
            });
        };
    </script>
</body>
</html>