import { prisma } from '@/lib/database/mysql-client'

async function main() {
  console.log('开始创建舆情分析相关表...')
  
  try {
    // 创建舆情分析缓存表
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS sentiment_analysis_cache (
        id CHAR(36) NOT NULL DEFAULT (UUID()),
        user_id CHAR(36) NOT NULL,
        keyword VARCHAR(255) NOT NULL,
        total_notes INT DEFAULT 0,
        total_likes INT DEFAULT 0,
        total_comments INT DEFAULT 0,
        total_collects INT DEFAULT 0,
        positive_notes INT DEFAULT 0,
        negative_notes INT DEFAULT 0,
        neutral_notes INT DEFAULT 0,
        positive_comments INT DEFAULT 0,
        negative_comments INT DEFAULT 0,
        neutral_comments INT DEFAULT 0,
        top_notes_data JSON,
        keywords_cloud JSON,
        sentiment_distribution JSON,
        trend_data JSON,
        ai_summary_report TEXT,
        note_ids JSON,
        metadata JSON,
        status VARCHAR(50) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        PRIMARY KEY (id),
        UNIQUE KEY unique_user_keyword (user_id, keyword),
        INDEX idx_sentiment_analysis_user_id (user_id),
        INDEX idx_sentiment_analysis_keyword (keyword),
        INDEX idx_sentiment_analysis_status (status),
        INDEX idx_sentiment_analysis_created_at (created_at)
      )
    `
    
    console.log('✅ 创建 sentiment_analysis_cache 表成功')
    
    // 创建舆情分析详情表
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS sentiment_analysis_details (
        id CHAR(36) NOT NULL DEFAULT (UUID()),
        cache_id CHAR(36) NOT NULL,
        note_id VARCHAR(255) NOT NULL,
        sentiment VARCHAR(20) NOT NULL,
        confidence FLOAT DEFAULT 0.0,
        keywords JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        INDEX idx_sentiment_detail_cache_id (cache_id),
        INDEX idx_sentiment_detail_note_id (note_id)
      )
    `
    
    console.log('✅ 创建 sentiment_analysis_details 表成功')
    console.log('\n✨ 舆情分析功能数据库迁移完成！')
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })