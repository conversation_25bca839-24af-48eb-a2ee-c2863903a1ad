import { prisma } from '@/lib/database/mysql-client'

async function testBatchGenerator() {
  try {
    console.log('Testing batch generator database...')
    
    // 查询所有会话
    const sessions = await prisma.batchGenerationSession.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10
    })
    
    console.log(`\nFound ${sessions.length} batch sessions:`)
    sessions.forEach(session => {
      console.log(`- ${session.sessionName} (${session.id})`)
      console.log(`  User: ${session.userId}`)
      console.log(`  Generated: ${session.totalGenerated} notes`)
      console.log(`  Created: ${session.createdAt}`)
    })
    
    if (sessions.length > 0) {
      // 查询第一个会话的笔记
      const firstSession = sessions[0]
      const notes = await prisma.batchGeneratedNote.findMany({
        where: { batchSessionId: firstSession.id },
        take: 5
      })
      
      console.log(`\nNotes in session "${firstSession.sessionName}" (${firstSession.id}):`)
      console.log(`Found ${notes.length} notes`)
      notes.forEach(note => {
        console.log(`- ${note.title} (${note.id})`)
        console.log(`  Published: ${note.isAddedToPublish}`)
        console.log(`  Keywords: ${JSON.stringify(note.keywords)}`)
      })
      
      // 查询所有笔记看看batch_session_id
      console.log('\nChecking all notes batch_session_id:')
      const allNotes = await prisma.batchGeneratedNote.findMany({
        take: 5,
        select: {
          id: true,
          batchSessionId: true,
          title: true
        }
      })
      allNotes.forEach(note => {
        console.log(`- Note ${note.id}: session=${note.batchSessionId}`)
      })
    }
    
    // 统计总数
    const totalSessions = await prisma.batchGenerationSession.count()
    const totalNotes = await prisma.batchGeneratedNote.count()
    
    console.log(`\nTotal sessions: ${totalSessions}`)
    console.log(`Total notes: ${totalNotes}`)
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testBatchGenerator()