import { prisma } from '@/lib/database/mysql-client'

async function testSentimentAnalysis() {
  console.log('测试舆情分析表是否创建成功...\n')
  
  try {
    // 测试 sentiment_analysis_cache 表
    const cacheCount = await prisma.sentimentAnalysisCache.count()
    console.log(`✅ sentiment_analysis_cache 表已创建，当前记录数: ${cacheCount}`)
    
    // 测试 sentiment_analysis_details 表
    const detailsCount = await prisma.sentimentAnalysisDetail.count()
    console.log(`✅ sentiment_analysis_details 表已创建，当前记录数: ${detailsCount}`)
    
    // 获取表结构信息
    const tableInfo = await prisma.$queryRaw`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'sentiment_analysis_cache'
      ORDER BY ORDINAL_POSITION
    ` as any[]
    
    console.log('\n📊 sentiment_analysis_cache 表结构:')
    console.table(tableInfo.map(col => ({
      字段名: col.COLUMN_NAME,
      数据类型: col.DATA_TYPE,
      是否可空: col.IS_NULLABLE,
      键类型: col.COLUMN_KEY || '-'
    })))
    
    console.log('\n✨ 舆情分析功能数据库测试通过！')
    console.log('您现在可以开始使用舆情分析功能了。')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    console.log('\n请确保已经运行了数据库迁移:')
    console.log('  npm run db:push')
    console.log('或')
    console.log('  npx tsx scripts/migrate-sentiment-analysis.ts')
  } finally {
    await prisma.$disconnect()
  }
}

testSentimentAnalysis()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })